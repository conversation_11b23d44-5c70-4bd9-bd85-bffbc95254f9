# LLM Provider Säkerhetsriktlinjer

## Översikt

Detta dokument beskriver säkerhetsprinciperna för LLM provider-arkitekturen i RPA-applikationen. Dessa riktlinjer säkerställer att systemet inte kan byta provider utan explicit användaråtgärd.

## Grundläggande Säkerhetsprinciper

### 1. Ingen Automatisk Provider-switching

**REGEL**: Systemet får ALDRIG byta LLM provider automatiskt.

**Implementation**:
- Om en provider misslyckas kastas ett fel
- Ingen fallback-logik mellan olika providers
- Användaren måste manuellt ändra `LLM_PROVIDER` miljövariabel

**Exempel på FÖRBJUDEN kod**:
```typescript
// ❌ DETTA ÄR FÖRBJUDET
if (providerType === 'azure' && azureFails) {
  // Byt till OpenAI automatiskt
  return new OpenAIProvider();
}
```

**Exempel på KORREKT kod**:
```typescript
// ✅ DETTA ÄR KORREKT
if (providerType === 'azure' && azureFails) {
  // Kasta fel istället för att byta provider
  throw new Error('Azure provider configuration failed');
}
```

### 2. Explicit Konfiguration

**REGEL**: All provider-konfiguration ska vara explicit via miljövariabler.

**Tillåtna konfigurationsmetoder**:
- Miljövariabler i `.env` fil
- Systemets miljövariabler
- Container/deployment konfiguration

**FÖRBJUDNA konfigurationsmetoder**:
- API endpoints som ändrar provider
- Runtime-ändringar av provider
- Automatisk provider-detektering

### 3. Modell Fallbacks (Tillåtet inom samma provider)

**REGEL**: Fallback mellan modeller är tillåtet ENDAST inom samma provider.

**Tillåtet**:
```typescript
// ✅ Fallback från gpt-4o-mini till o1-mini inom OpenAI
if (!supportedModels.includes(defaultModel)) {
  const fallbackModel = getFallbackModel();
  if (supportedModels.includes(fallbackModel)) {
    // Använd fallback-modell inom samma provider
    return fallbackModel;
  }
}
```

**Förbjudet**:
```typescript
// ❌ Fallback från Azure till OpenAI
if (azureModelFails) {
  return openaiProvider.createCompletion(); // FÖRBJUDET
}
```

## Säkerhetsvalidering

### Startup Validering

Vid systemstart ska följande valideras:

1. **Provider Konfiguration**
   ```typescript
   // Validera att vald provider är korrekt konfigurerad
   const provider = LLMProviderFactory.getInstance();
   if (!provider.isConfigured()) {
     throw new Error(`LLM provider ${provider.name} is not configured`);
   }
   ```

2. **API Nyckel Validering**
   ```typescript
   // Testa anslutning utan fallback
   const testResult = await provider.testConnection();
   if (!testResult) {
     throw new Error(`LLM provider ${provider.name} connection test failed`);
   }
   ```

### Runtime Säkerhet

**Health Check Endpoint**:
- `/api/ai-assistant/health` visar aktuell provider
- Inga endpoints för att ändra provider
- Alla provider-ändringar kräver server-restart

**Logging och Monitoring**:
```typescript
// Logga alla provider-operationer
console.log(`🔒 Using LLM provider: ${provider.name}`);
console.log(`🔒 Provider configured: ${provider.isConfigured()}`);
```

## Säker Provider-byte

### Manuell Process

För att byta provider säkert:

1. **Stoppa applikationen**
   ```bash
   # Stoppa backend-servern
   npm run stop
   ```

2. **Uppdatera miljövariabler**
   ```bash
   # I .env fil
   LLM_PROVIDER=azure  # Ändra från 'openai' till 'azure'
   
   # Lägg till Azure-konfiguration
   AZURE_OPENAI_API_KEY=your_azure_key
   AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
   AZURE_OPENAI_API_VERSION=2024-02-15-preview
   ```

3. **Validera konfiguration**
   ```bash
   # Testa att nya provider-inställningar fungerar
   npm run test:llm
   ```

4. **Starta applikationen**
   ```bash
   # Starta med nya provider-inställningar
   npm run dev
   ```

5. **Verifiera byte**
   ```bash
   # Kontrollera health endpoint
   curl http://localhost:3002/api/ai-assistant/health
   ```

### Rollback Process

Om provider-byte misslyckas:

1. **Återställ miljövariabler**
   ```bash
   # Återgå till tidigare fungerande konfiguration
   LLM_PROVIDER=openai
   ```

2. **Starta om systemet**
   ```bash
   npm run dev
   ```

## Säkerhetsövervakning

### Loggar att övervaka

```bash
# Provider-initialisering
🔒 Using LLM provider: openai
🔒 Provider configured: true

# Fel som indikerar säkerhetsproblem
🚨 Failed to create azure provider
❌ LLM provider not configured
```

### Alerts att sätta upp

1. **Provider-fel**: Alert om provider misslyckas
2. **Konfigurationsfel**: Alert om API-nycklar är ogiltiga
3. **Oväntade provider-byten**: Alert om provider ändras utan deployment

## Compliance och Audit

### Audit Trail

Alla LLM-operationer ska loggas:
```typescript
console.log(`🔍 LLM Request: provider=${provider.name}, model=${model}`);
console.log(`🔍 LLM Response: success=${success}, tokens=${usage?.totalTokens}`);
```

### Säkerhetsreview

Regelbunden granskning av:
- Provider-konfiguration
- API-nyckel rotation
- Säkerhetsloggar
- Compliance med säkerhetspolicies

## Utvecklingsriktlinjer

### Code Review Checklist

- [ ] Ingen automatisk provider-switching
- [ ] Alla provider-ändringar via miljövariabler
- [ ] Korrekt felhantering utan fallbacks
- [ ] Säkerhetsloggning implementerad

### Testing

```typescript
// Test att säkerställa ingen automatisk fallback
test('should not fallback to different provider', async () => {
  process.env.LLM_PROVIDER = 'azure';
  process.env.AZURE_OPENAI_API_KEY = 'invalid_key';
  
  expect(() => LLMProviderFactory.getInstance())
    .toThrow('Azure provider configuration failed');
});
```

## Sammanfattning

**VIKTIGA SÄKERHETSPRINCIPER**:
1. ❌ Ingen automatisk provider-switching
2. ✅ Explicit konfiguration via miljövariabler
3. ✅ Modell-fallbacks inom samma provider
4. ✅ Manuell provider-byte process
5. ✅ Säkerhetsloggning och monitoring

Dessa riktlinjer säkerställer att systemet aldrig byter LLM provider utan explicit användaråtgärd och minskar säkerhetsrisker relaterade till oväntade provider-byten.
