# RPA Project Templates

Det<PERSON> är template-systemet för RPA-projektet som gör det enkelt att skapa nya steg och runners.

## Översikt

Template-systemet består av:

- **new-step/**: Templates för att skapa nya RPA-steg
- **new-runner/**: Templates för att skapa nya runners
- **examples/**: Praktiska exempel på hur templates används

## Användning

### Skapa nytt steg

```bash
# Använd step-generator scriptet
node tools/generate-step.js --type=myNewStep --category=interaction --runner=playwright

# Eller kopiera templates manuellt från new-step/ mappen
```

### Skapa ny runner

```bash
# Kopiera templates från new-runner/ mappen
cp -r docs/templates/new-runner/ backend/src/runners/my-new-runner/
```

## Template-variabler

Templates använder följande variabler som ersätts automatiskt:

- `{{STEP_TYPE}}` - <PERSON><PERSON> på steget (t.ex. "myNewStep")
- `{{STEP_CATEGORY}}` - <PERSON><PERSON><PERSON> (navigation, interaction, ai, api)
- `{{RUNNER_TYPE}}` - Vilken runner som ska användas
- `{{STEP_DESCRIPTION}}` - Beskrivning av steget
- `{{PASCAL_CASE}}` - PascalCase version av namnet
- `{{KEBAB_CASE}}` - kebab-case version av namnet

## Struktur

```
docs/templates/
├── README.md                    # Detta dokument
├── new-step/                    # Templates för nya steg
│   ├── step-type.ts.template
│   ├── validator.ts.template
│   ├── runner-method.ts.template
│   ├── editor.tsx.template
│   └── toolbar-entry.ts.template
├── new-runner/                  # Templates för nya runners
│   ├── runner.ts.template
│   ├── step-executors.ts.template
│   ├── tests.ts.template
│   └── README.md.template
└── examples/                    # Praktiska exempel
    ├── custom-step-example/
    ├── custom-runner-example/
    └── integration-examples/
```

## Nästa steg

1. Läs dokumentationen i `docs/development/`
2. Använd templates för att skapa nya komponenter
3. Följ konventionerna i `docs/development/conventions.md`
4. Testa dina ändringar enligt `docs/development/troubleshooting.md`
