#!/bin/bash

echo "🚀 Starting RPA Development Environment..."

# Check if Redis is running
if ! redis-cli ping > /dev/null 2>&1; then
    echo "📦 Starting Redis..."
    docker run -d --name rpa-redis -p 6379:6379 redis:alpine
    sleep 2
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm run setup
fi

# Start development servers
echo "🔧 Starting development servers..."
npm run dev
