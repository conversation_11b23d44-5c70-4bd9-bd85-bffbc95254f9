import { ReactNode } from 'react'
import { RpaStep } from '@rpa-project/shared'

export interface BaseStepEditorProps {
  step: RpaStep
  onSave: (step: RpaStep) => void
  onCancel: () => void
  compact?: boolean
  steps?: RpaStep[]
  currentStepIndex?: number
}

export interface StepEditorFieldProps {
  label: string
  children: ReactNode
  fullWidth?: boolean
  compact?: boolean
}

export function StepEditorField({ label, children, fullWidth = false, compact = false }: StepEditorFieldProps) {
  return (
    <div style={{
      marginBottom: compact ? '0.75rem' : '1rem',
      flex: compact && !fullWidth ? '1 1 300px' : '1 1 100%',
      minWidth: compact && !fullWidth ? '250px' : 'auto'
    }}>
      <label className="form-label" style={{
        fontSize: compact ? '0.75rem' : '0.875rem',
        marginBottom: compact ? '0.375rem' : '0.5rem'
      }}>
        {label}
      </label>
      {children}
    </div>
  )
}

export function getInputStyle(compact: boolean = false) {
  return {
    padding: compact ? '0.375rem 0.5rem' : '0.5rem 0.75rem',
    fontSize: compact ? '0.75rem' : '0.875rem'
  }
}

export function getSelectStyle(compact: boolean = false) {
  return {
    ...getInputStyle(compact),
    backgroundColor: 'white'
  }
}

export interface StepEditorLayoutProps {
  title: string
  errors: string[]
  children: ReactNode
  onSave: () => void
  onCancel: () => void
  compact?: boolean
}

export function StepEditorLayout({ 
  title, 
  errors, 
  children, 
  onSave, 
  onCancel, 
  compact = false 
}: StepEditorLayoutProps) {
  if (compact) {
    return (
      <div>
        {/* Header with step type */}
        <div style={{
          marginBottom: '1rem',
          paddingBottom: '0.75rem',
          borderBottom: '1px solid #e5e7eb'
        }}>
          <h4 style={{
            margin: 0,
            fontSize: '1rem',
            fontWeight: '500',
            color: '#1a0f0f'
          }}>
            {title}
          </h4>
        </div>

        {errors.length > 0 && (
          <div style={{
            marginBottom: '1rem',
            padding: '0.75rem',
            backgroundColor: '#fef2f2',
            border: '1px solid #fecaca',
            borderRadius: '0.5rem',
            fontSize: '0.875rem'
          }}>
            <ul style={{ margin: 0, paddingLeft: '1rem', color: '#991b1b' }}>
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </div>
        )}

        {/* Form fields in responsive columns */}
        <div style={{
          display: 'flex',
          flexWrap: 'wrap',
          gap: '1rem',
          marginBottom: '1.5rem'
        }}>
          {children}
        </div>

        {/* Action buttons */}
        <div style={{
          display: 'flex',
          gap: '0.75rem',
          justifyContent: 'flex-end',
          paddingTop: '1rem',
          borderTop: '1px solid #e5e7eb'
        }}>
          <button
            onClick={onCancel}
            className="action-button-small secondary"
          >
            Avbryt
          </button>
          <button
            onClick={onSave}
            className="action-button-small primary"
          >
            Spara
          </button>
        </div>
      </div>
    )
  }

  // Original modal layout for non-compact mode
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div style={{ position: 'relative', maxWidth: '28rem', width: '100%', margin: '0 1rem', maxHeight: '80vh' }}>
        <div className="bg-white rounded-lg shadow-xl overflow-y-auto custom-scrollbar" style={{ height: '100%' }}>
          <div className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {title}
            </h3>

            {errors.length > 0 && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded">
                <ul className="text-sm text-red-700">
                  {errors.map((error, index) => (
                    <li key={index}>• {error}</li>
                  ))}
                </ul>
              </div>
            )}

            <div className="space-y-4">
              {children}
            </div>

            <div className="flex gap-2 mt-6">
              <button
                onClick={onSave}
                className="btn btn-primary flex-1"
              >
                Save Changes
              </button>
              <button
                onClick={onCancel}
                className="btn btn-outline flex-1"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
