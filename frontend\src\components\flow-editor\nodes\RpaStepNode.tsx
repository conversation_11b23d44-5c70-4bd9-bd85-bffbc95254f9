import { memo, useState } from 'react'
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow'
import { RpaStep, getStepLabel } from '@rpa-project/shared'
import { StepEditor } from '../StepEditor'

interface RpaStepNodeData {
  step: RpaStep
  label: string
}

interface RpaStepNodeProps extends NodeProps<RpaStepNodeData> {
  onUpdateStep?: (nodeId: string, newData: any) => void
}

export const RpaStepNode = memo(({ data, selected, id, onUpdateStep }: RpaStepNodeProps) => {
  const [showEditor, setShowEditor] = useState(false)
  const { step } = data

  const getStepIcon = (stepType: string): string => {
    switch (stepType) {
      case 'navigate': return '🌐'
      case 'goBack': return '⬅️'
      case 'goForward': return '➡️'
      case 'reload': return '🔄'
      case 'click': return '👆'
      case 'fill': return '✏️'
      case 'type': return '⌨️'
      case 'selectOption': return '📋'
      case 'check': return '☑️'
      case 'uncheck': return '☐'
      case 'waitForSelector': return '👁️'
      case 'waitForTimeout': return '⏰'
      case 'extractText': return '📝'
      case 'takeScreenshot': return '📸'
      case 'downloadFile': return '📥'
      case 'conditionalClick': return '🔀'
      case 'fillPassword': return '🔑'
      case 'fill2FA': return '🔐'
      case 'extractPdfValues': return '📄'
      case 'fortnoxCreateVoucher': return '🧾'
      case 'fortnoxUploadFile': return '📤'
      case 'fortnoxAttachFileToVoucher': return '📎'
      case 'fortnoxUploadAndCreateVoucher': return '📋'
      default: return '⚙️'
    }
  }

  const getStepColor = (stepType: string): { borderColor: string; backgroundColor: string; accentColor: string } => {
    switch (stepType) {
      case 'navigate':
      case 'goBack':
      case 'goForward':
      case 'reload':
        return { borderColor: '#3b82f6', backgroundColor: '#eff6ff', accentColor: '#3b82f6' }
      case 'click':
      case 'fill':
      case 'type':
      case 'selectOption':
      case 'check':
      case 'uncheck':
      case 'conditionalClick':
        return { borderColor: '#10b981', backgroundColor: '#ecfdf5', accentColor: '#10b981' }
      case 'waitForSelector':
      case 'waitForTimeout':
        return { borderColor: '#f59e0b', backgroundColor: '#fffbeb', accentColor: '#f59e0b' }
      case 'extractText':
      case 'takeScreenshot':
      case 'downloadFile':
        return { borderColor: '#8b5cf6', backgroundColor: '#f5f3ff', accentColor: '#8b5cf6' }
      case 'fillPassword':
      case 'fill2FA':
        return { borderColor: '#dc2626', backgroundColor: '#fef2f2', accentColor: '#dc2626' }
      case 'extractPdfValues':
        return { borderColor: '#f97316', backgroundColor: '#fff7ed', accentColor: '#f97316' }
      case 'fortnoxCreateVoucher':
        return { borderColor: '#06b6d4', backgroundColor: '#ecfeff', accentColor: '#06b6d4' }
      default:
        return { borderColor: '#6b7280', backgroundColor: '#f9fafb', accentColor: '#6b7280' }
    }
  }

  const handleDoubleClick = () => {
    setShowEditor(true)
  }

  const stepColors = getStepColor(step.type)

  return (
    <>
      <div
        style={{
          minWidth: showEditor ? '320px' : '200px',
          maxWidth: showEditor ? '400px' : '250px',
          width: showEditor ? '320px' : 'auto',
          padding: '0.75rem 1rem',
          backgroundColor: 'white',
          borderRadius: '0.75rem',
          boxShadow: selected
            ? '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 0 0 3px rgba(59, 130, 246, 0.1)'
            : '0 1px 3px rgba(0, 0, 0, 0.1)',
          border: `2px solid ${stepColors.borderColor}`,
          cursor: showEditor ? 'default' : 'grab',
          transition: 'all 0.3s ease',
          position: 'relative',
          // Ensure expanded nodes appear above others
          zIndex: showEditor ? 9999 : 'auto'
        }}
        onDoubleClick={handleDoubleClick}
        onMouseEnter={(e) => {
          if (!selected && !showEditor) {
            e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            e.currentTarget.style.transform = 'translateY(-1px)'
          }
        }}
        onMouseLeave={(e) => {
          if (!selected && !showEditor) {
            e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)'
            e.currentTarget.style.transform = 'translateY(0)'
          }
        }}
        onMouseDown={(e) => {
          // Don't prevent default to allow React Flow dragging
          if (showEditor) {
            e.stopPropagation()
          }
        }}
      >
        <Handle
          type="target"
          position={Position.Top}
          style={{
            width: '12px',
            height: '12px',
            backgroundColor: stepColors.accentColor,
            border: '2px solid white',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
          }}
        />

        {/* Category indicator */}
        <div style={{
          position: 'absolute',
          top: '0.5rem',
          right: '0.5rem',
          width: '8px',
          height: '8px',
          backgroundColor: stepColors.accentColor,
          borderRadius: '50%'
        }} />

        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
          <span style={{ fontSize: '1.125rem' }}>{getStepIcon(step.type)}</span>
          <div style={{ flex: 1, minWidth: 0 }}>
            <div style={{
              fontWeight: '600',
              color: '#111827',
              fontSize: '0.875rem',
              marginBottom: '0.25rem'
            }}>
              {step.type}
            </div>
            <div style={{
              fontSize: '0.75rem',
              color: '#6b7280',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}>
              {getStepLabel(step)}
            </div>
          </div>
        </div>

        {step.description && !showEditor && (
          <div style={{
            marginTop: '0.5rem',
            fontSize: '0.75rem',
            color: '#9ca3af',
            fontStyle: 'italic',
            lineHeight: '1.4'
          }}>
            {step.description}
          </div>
        )}

        {/* Inline Editor */}
        {showEditor && (
          <div style={{
            marginTop: '0.75rem',
            paddingTop: '0.75rem',
            borderTop: `1px solid ${stepColors.borderColor}`,
            cursor: 'default'
          }}
          onClick={(e) => e.stopPropagation()}
          >
            <StepEditor
              step={step}
              onSave={(updatedStep) => {
                if (onUpdateStep) {
                  onUpdateStep(id, {
                    step: updatedStep,
                    label: getStepLabel(updatedStep)
                  })
                } else {
                  data.step = updatedStep
                  data.label = getStepLabel(updatedStep)
                }
                setShowEditor(false)
              }}
              onCancel={() => setShowEditor(false)}
              compact={true}
            />
          </div>
        )}

        <Handle
          type="source"
          position={Position.Bottom}
          style={{
            width: '12px',
            height: '12px',
            backgroundColor: stepColors.accentColor,
            border: '2px solid white',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
          }}
        />
      </div>
    </>
  )
})
