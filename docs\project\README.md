# RPA Project

Ett komplett RPA-projekt byggt med TypeScript, React Flow, och Playwright.

## Arkitektur

- **Frontend**: React + TypeScript + React Flow för visuell flödesdesign
- **Backend**: Node.js + Express + TypeScript för API och flödeskörning
- **Queue**: BullMQ + Redis för asynkron jobbhantering
- **Automation**: Playwright för web automation
- **Shared**: Gemensamma TypeScript-typer

## Senaste förbättringar

### ✅ Fixad variabelväljare (v1.2.0)
Variabelväljaren visar nu korrekt alla variabler från steg som skapar variabler, även när default-variabelnamn används (t.ex. `extractedText_1`, `screenshot_2`). Frontend använder nu samma logik som backend för konsistent variabeldetektering.

## Projektstruktur

```
/rpa-project
├── frontend/        # React + React Flow
├── backend/         # Express + Playwright + BullMQ
├── shared/          # Delade TypeScript-typer
├── docker/          # Docker konfiguration
└── package.json     # Root workspace
```

## Snabbstart

1. Installera dependencies:
```bash
npm run install:all
```

2. Starta Redis (krävs för BullMQ):
```bash
# Med Docker
docker run -d -p 6379:6379 redis:alpine

# Eller med Docker Compose (kommer senare)
docker-compose up redis
```

3. Starta utvecklingsservrar:
```bash
npm run dev
```

Detta startar:
- Frontend på http://localhost:3000
- Backend API på http://localhost:3001

## Utveckling

### Frontend (React + React Flow)
- Visuell drag-and-drop designer för RPA-flöden
- Export/import av flöden som JSON
- Realtidspreview av flöden

### Backend (Express + Playwright)
- REST API för flödeshantering
- JSON-till-Playwright parser
- BullMQ integration för asynkron körning

### Shared Types
- Gemensamma TypeScript-definitioner
- RpaStep, RpaFlow och andra typer
- Används av både frontend och backend

## Kommande funktioner

- [ ] Docker Compose setup
- [ ] Användarautentisering
- [ ] Flödeshistorik och logging
- [ ] Schemalagd körning
- [ ] Fler RPA-kommandon
- [ ] Felhantering och retry-logik
