import { Job } from 'bullmq';
import { ExecuteFlowRequest } from '@rpa-project/shared';
import { getQueue } from './queue';
import { FlowJobData } from './worker';

export interface QueueStats {
  waiting: number;
  active: number;
  completed: number;
  failed: number;
  delayed: number;
  paused: boolean;
}

export interface JobQuery {
  status?: 'waiting' | 'active' | 'completed' | 'failed' | 'delayed';
  limit?: number;
  offset?: number;
}

export class QueueService {
  private get queue() {
    return getQueue();
  }

  async addFlowExecution(executionId: string, request: ExecuteFlowRequest): Promise<Job<FlowJobData>> {
    const jobData: FlowJobData = {
      ...request,
      executionId
    };

    const job = await this.queue.add(
      'execute-flow',
      jobData,
      {
        jobId: executionId, // Use execution ID as job ID for easy tracking
        priority: 1,
      }
    );

    console.log(`📋 Added flow execution job: ${job.id}`);
    return job;
  }

  async cancelExecution(executionId: string): Promise<boolean> {
    try {
      const job = await this.queue.getJob(executionId);
      if (!job) {
        console.log(`⚠️ Job ${executionId} not found in queue (may have already completed)`);
        return false;
      }

      // Check job state
      const state = await job.getState();
      console.log(`🔍 Job ${executionId} current state: ${state}`);

      // If job is active (currently running), we need to handle it differently
      if (state === 'active') {
        console.log(`⚠️ Job ${executionId} is currently active, marking for cancellation`);
        // The job will check execution status and stop processing
        // We don't remove active jobs as it can cause issues
        return true;
      }

      // For waiting, delayed, or failed jobs, we can safely remove them
      if (state === 'waiting' || state === 'delayed' || state === 'failed') {
        await job.remove();
        console.log(`🛑 Removed job ${executionId} from queue (state: ${state})`);
        return true;
      }

      // For completed jobs, no action needed
      if (state === 'completed') {
        console.log(`✅ Job ${executionId} already completed`);
        return true;
      }

      console.log(`⚠️ Job ${executionId} in unexpected state: ${state}`);
      return false;
    } catch (error) {
      console.error(`Failed to cancel execution ${executionId}:`, error);
      return false;
    }
  }

  async getQueueStats(): Promise<QueueStats> {
    const waiting = await this.queue.getWaiting();
    const active = await this.queue.getActive();
    const completed = await this.queue.getCompleted();
    const failed = await this.queue.getFailed();
    const delayed = await this.queue.getDelayed();
    const isPaused = await this.queue.isPaused();

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      delayed: delayed.length,
      paused: isPaused
    };
  }

  async getJobs(query: JobQuery = {}): Promise<Job[]> {
    const { status = 'waiting', limit = 50, offset = 0 } = query;

    let jobs: Job[] = [];

    switch (status) {
      case 'waiting':
        jobs = await this.queue.getWaiting(offset, offset + limit - 1);
        break;
      case 'active':
        jobs = await this.queue.getActive(offset, offset + limit - 1);
        break;
      case 'completed':
        jobs = await this.queue.getCompleted(offset, offset + limit - 1);
        break;
      case 'failed':
        jobs = await this.queue.getFailed(offset, offset + limit - 1);
        break;
      case 'delayed':
        jobs = await this.queue.getDelayed(offset, offset + limit - 1);
        break;
    }

    return jobs;
  }

  async getJobById(jobId: string): Promise<Job | null> {
    return (await this.queue.getJob(jobId)) || null;
  }

  async retryJob(jobId: string): Promise<void> {
    const job = await this.queue.getJob(jobId);
    if (!job) {
      throw new Error('Job not found');
    }

    await job.retry();
    console.log(`🔄 Retrying job: ${jobId}`);
  }

  async removeJob(jobId: string): Promise<void> {
    const job = await this.queue.getJob(jobId);
    if (!job) {
      throw new Error('Job not found');
    }

    await job.remove();
    console.log(`🗑️ Removed job: ${jobId}`);
  }

  async pauseQueue(): Promise<void> {
    await this.queue.pause();
    console.log('⏸️ Queue paused');
  }

  async resumeQueue(): Promise<void> {
    await this.queue.resume();
    console.log('▶️ Queue resumed');
  }

  async cleanCompletedJobs(olderThanHours: number = 24): Promise<number> {
    const olderThanMs = olderThanHours * 60 * 60 * 1000;
    const jobs = await this.queue.clean(olderThanMs, 100, 'completed');
    
    console.log(`🧹 Cleaned ${jobs.length} completed jobs older than ${olderThanHours} hours`);
    return jobs.length;
  }

  async cleanFailedJobs(olderThanHours: number = 24): Promise<number> {
    const olderThanMs = olderThanHours * 60 * 60 * 1000;
    const jobs = await this.queue.clean(olderThanMs, 100, 'failed');
    
    console.log(`🧹 Cleaned ${jobs.length} failed jobs older than ${olderThanHours} hours`);
    return jobs.length;
  }

  async getJobProgress(jobId: string): Promise<number | object | null> {
    const job = await this.queue.getJob(jobId);
    if (!job) {
      return null;
    }

    return job.progress;
  }

  async getJobLogs(jobId: string): Promise<string[]> {
    const job = await this.queue.getJob(jobId);
    if (!job) {
      return [];
    }

    // BullMQ doesn't have a logs property, we need to implement logging differently
    // For now, return empty array - this should be implemented with proper logging
    return [];
  }

  // Utility methods
  async drainQueue(): Promise<void> {
    await this.queue.drain();
    console.log('🚰 Queue drained');
  }

  async obliterateQueue(): Promise<void> {
    await this.queue.obliterate();
    console.log('💥 Queue obliterated');
  }
}
