// Execution types
export interface FlowExecution {
  id: string;
  flowId: string;
  customerId: string;
  status: ExecutionStatus;
  startedAt: Date;
  completedAt?: Date;
  error?: string;
  logs: ExecutionLog[];
  results?: Record<string, any>;
  retryInfo?: RetryInfo;
}

export interface RetryInfo {
  attempts: number;
  maxAttempts: number;
  isRetry: boolean;
}

export type ExecutionStatus =
  | 'pending'
  | 'running'
  | 'completed'
  | 'failed'
  | 'cancelled';

export interface ExecutionLog {
  timestamp: Date;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  stepId?: string;
  data?: any;
}
