<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test VNC Popup</title>
</head>
<body>
    <h1>Test VNC Popup Functionality</h1>
    <button id="testPopup">Test VNC Popup</button>
    <div id="result"></div>

    <script>
        document.getElementById('testPopup').addEventListener('click', async () => {
            const resultDiv = document.getElementById('result');
            
            try {
                // Simulate the API call that returns VNC info
                const mockResponse = {
                    success: true,
                    data: {
                        id: "test-execution",
                        vncInfo: {
                            vncUrl: "vnc://localhost:5900",
                            webUrl: "http://localhost:6080/vnc.html?host=localhost&port=6080&autoconnect=true&resize=off"
                        }
                    }
                };

                // Test the VNC popup logic (same as in FlowEditor)
                const isNonHeadless = true; // Simulate headless = false
                
                if (isNonHeadless && mockResponse.data.vncInfo?.webUrl) {
                    const vncUrl = mockResponse.data.vncInfo.webUrl;
                    console.log('🖥️ Opening VNC viewer:', vncUrl);
                    
                    // Open VNC in a new window/tab
                    const vncWindow = window.open(
                        vncUrl,
                        'rpa-vnc-viewer',
                        'width=1280,height=720,scrollbars=yes,resizable=yes,toolbar=no,menubar=no,location=no,status=no'
                    );
                    
                    if (!vncWindow) {
                        // Fallback if popup was blocked
                        console.warn('Popup blocked, showing VNC URL to user');
                        resultDiv.innerHTML = `
                            <div style="color: orange; margin-top: 10px;">
                                ⚠️ Popup blocked by browser. Please open manually:<br>
                                <a href="${vncUrl}" target="_blank">${vncUrl}</a>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <div style="color: green; margin-top: 10px;">
                                ✅ VNC popup opened successfully!<br>
                                URL: ${vncUrl}
                            </div>
                        `;
                    }
                } else {
                    resultDiv.innerHTML = `
                        <div style="color: red; margin-top: 10px;">
                            ❌ No VNC info available or headless mode enabled
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error testing VNC popup:', error);
                resultDiv.innerHTML = `
                    <div style="color: red; margin-top: 10px;">
                        ❌ Error: ${error.message}
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
