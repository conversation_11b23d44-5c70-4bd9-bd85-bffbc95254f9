/**
 * Tester för step-generator scriptet
 */

const { generateCaseVariations, processTemplate, validateArgs } = require('../generate-step');

describe('Step Generator', () => {
  describe('generateCaseVariations', () => {
    test('ska generera korrekta case-variationer', () => {
      const result = generateCaseVariations('myNewStep');
      
      expect(result).toEqual({
        camelCase: 'myNewStep',
        pascalCase: 'MyNewStep',
        kebabCase: 'my-new-step',
        upperCase: 'MY_NEW_STEP'
      });
    });

    test('ska hantera enkla namn', () => {
      const result = generateCaseVariations('click');
      
      expect(result).toEqual({
        camelCase: 'click',
        pascalCase: 'Click',
        kebabCase: 'click',
        upperCase: 'CLICK'
      });
    });

    test('ska hantera komplexa namn', () => {
      const result = generateCaseVariations('extractTableDataFromHTML');
      
      expect(result).toEqual({
        camelCase: 'extractTableDataFromHTML',
        pascalCase: 'ExtractTableDataFromHTML',
        kebabCase: 'extract-table-data-from-h-t-m-l',
        upperCase: 'EXTRACT_TABLE_DATA_FROM_H_T_M_L'
      });
    });
  });

  describe('processTemplate', () => {
    test('ska ersätta template-variabler', () => {
      const template = `
export interface {{PASCAL_CASE}}Step extends RpaStepBase {
  type: '{{STEP_TYPE}}';
  category: '{{STEP_CATEGORY}}';
}
      `.trim();

      const variables = {
        PASCAL_CASE: 'MyNewStep',
        STEP_TYPE: 'myNewStep',
        STEP_CATEGORY: 'interaction'
      };

      const result = processTemplate(template, variables);
      
      expect(result).toContain("export interface MyNewStepStep extends RpaStepBase");
      expect(result).toContain("type: 'myNewStep'");
      expect(result).toContain("category: 'interaction'");
    });

    test('ska hantera flera förekomster av samma variabel', () => {
      const template = '{{NAME}} and {{NAME}} are the same';
      const variables = { NAME: 'test' };

      const result = processTemplate(template, variables);
      
      expect(result).toBe('test and test are the same');
    });

    test('ska lämna okända variabler orörda', () => {
      const template = '{{KNOWN}} and {{UNKNOWN}}';
      const variables = { KNOWN: 'test' };

      const result = processTemplate(template, variables);
      
      expect(result).toBe('test and {{UNKNOWN}}');
    });
  });

  describe('validateArgs', () => {
    test('ska acceptera giltiga argument', () => {
      const validArgs = {
        type: 'myNewStep',
        category: 'interaction',
        runner: 'playwright'
      };

      expect(validateArgs(validArgs)).toBe(true);
    });

    test('ska kräva obligatoriska fält', () => {
      const incompleteArgs = {
        type: 'myNewStep',
        category: 'interaction'
        // runner saknas
      };

      expect(validateArgs(incompleteArgs)).toBe(false);
    });

    test('ska validera kategori', () => {
      const invalidCategory = {
        type: 'myNewStep',
        category: 'invalidCategory',
        runner: 'playwright'
      };

      expect(validateArgs(invalidCategory)).toBe(false);
    });

    test('ska validera runner', () => {
      const invalidRunner = {
        type: 'myNewStep',
        category: 'interaction',
        runner: 'invalidRunner'
      };

      expect(validateArgs(invalidRunner)).toBe(false);
    });

    test('ska validera step-typ format', () => {
      const invalidStepType = {
        type: 'MyNewStep', // Ska börja med liten bokstav
        category: 'interaction',
        runner: 'playwright'
      };

      expect(validateArgs(invalidStepType)).toBe(false);
    });

    test('ska acceptera giltiga kategorier', () => {
      const validCategories = [
        'navigation', 'interaction', 'waiting', 'extraction', 
        'credentials', 'files', 'conditional', 'ai', 'api'
      ];

      validCategories.forEach(category => {
        const args = {
          type: 'testStep',
          category: category,
          runner: 'playwright'
        };
        expect(validateArgs(args)).toBe(true);
      });
    });

    test('ska acceptera giltiga runners', () => {
      const validRunners = ['playwright', 'ai', 'api'];

      validRunners.forEach(runner => {
        const args = {
          type: 'testStep',
          category: 'interaction',
          runner: runner
        };
        expect(validateArgs(args)).toBe(true);
      });
    });
  });

  describe('Integration tests', () => {
    test('ska generera korrekta variabler för template-processing', () => {
      const stepType = 'extractTableData';
      const category = 'extraction';
      const runner = 'playwright';

      const caseVariations = generateCaseVariations(stepType);
      const variables = {
        STEP_TYPE: stepType,
        STEP_CATEGORY: category,
        RUNNER_TYPE: runner,
        STEP_DESCRIPTION: `${caseVariations.pascalCase} Steg`,
        PASCAL_CASE: caseVariations.pascalCase,
        KEBAB_CASE: caseVariations.kebabCase,
        UPPER_CASE: caseVariations.upperCase
      };

      expect(variables.STEP_TYPE).toBe('extractTableData');
      expect(variables.PASCAL_CASE).toBe('ExtractTableData');
      expect(variables.KEBAB_CASE).toBe('extract-table-data');
      expect(variables.UPPER_CASE).toBe('EXTRACT_TABLE_DATA');
      expect(variables.STEP_DESCRIPTION).toBe('ExtractTableData Steg');
    });

    test('ska hantera edge cases för namngivning', () => {
      const edgeCases = [
        { input: 'a', expected: { camelCase: 'a', pascalCase: 'A', kebabCase: 'a', upperCase: 'A' } },
        { input: 'API', expected: { camelCase: 'API', pascalCase: 'API', kebabCase: 'a-p-i', upperCase: 'A_P_I' } },
        { input: 'html2PDF', expected: { camelCase: 'html2PDF', pascalCase: 'Html2PDF', kebabCase: 'html2-p-d-f', upperCase: 'HTML2_P_D_F' } }
      ];

      edgeCases.forEach(({ input, expected }) => {
        const result = generateCaseVariations(input);
        expect(result).toEqual(expected);
      });
    });
  });

  describe('Error handling', () => {
    test('ska hantera tomma strängar', () => {
      expect(() => generateCaseVariations('')).not.toThrow();
      
      const result = generateCaseVariations('');
      expect(result.camelCase).toBe('');
      expect(result.pascalCase).toBe('');
      expect(result.kebabCase).toBe('');
      expect(result.upperCase).toBe('');
    });

    test('ska hantera null/undefined input gracefully', () => {
      expect(() => generateCaseVariations(null)).toThrow();
      expect(() => generateCaseVariations(undefined)).toThrow();
    });

    test('ska hantera template utan variabler', () => {
      const template = 'No variables here';
      const variables = { UNUSED: 'value' };

      const result = processTemplate(template, variables);
      expect(result).toBe('No variables here');
    });
  });

  describe('Performance tests', () => {
    test('ska hantera stora templates effektivt', () => {
      const largeTemplate = '{{VAR}}'.repeat(1000);
      const variables = { VAR: 'test' };

      const startTime = Date.now();
      const result = processTemplate(largeTemplate, variables);
      const endTime = Date.now();

      expect(result).toBe('test'.repeat(1000));
      expect(endTime - startTime).toBeLessThan(100); // Ska ta mindre än 100ms
    });

    test('ska hantera många variabler effektivt', () => {
      const template = Array.from({ length: 100 }, (_, i) => `{{VAR${i}}}`).join(' ');
      const variables = Object.fromEntries(
        Array.from({ length: 100 }, (_, i) => [`VAR${i}`, `value${i}`])
      );

      const startTime = Date.now();
      const result = processTemplate(template, variables);
      const endTime = Date.now();

      expect(result).toContain('value0');
      expect(result).toContain('value99');
      expect(endTime - startTime).toBeLessThan(100); // Ska ta mindre än 100ms
    });
  });
});

// Mock console för att undvika spam i test output
const originalConsole = console;
beforeAll(() => {
  console.log = jest.fn();
  console.error = jest.fn();
  console.warn = jest.fn();
});

afterAll(() => {
  console.log = originalConsole.log;
  console.error = originalConsole.error;
  console.warn = originalConsole.warn;
});
