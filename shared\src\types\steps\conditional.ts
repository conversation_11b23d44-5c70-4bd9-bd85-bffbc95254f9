import { RpaStepBase } from './base';

// Forward declaration to avoid circular dependency
type RpaStep = any; // Will be properly typed when assembled in index.ts

export interface IfElementExistsStep extends RpaStepBase {
  type: 'ifElementExists';
  selector: string;
  thenSteps: RpaStep[];
  elseSteps?: RpaStep[];
}

export interface ConditionalClickStep extends RpaStepBase {
  type: 'conditionalClick';
  selector: string;
  condition: 'exists' | 'enabled' | 'disabled';
  clickIfTrue?: boolean; // default true
  button?: 'left' | 'right' | 'middle';
  force?: boolean;
}
