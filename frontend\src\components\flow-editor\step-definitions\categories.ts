export interface StepDefinition {
  type: string
  name: string
  icon: string
  description: string
}

export interface StepCategory {
  name: string
  icon: string
  steps: StepDefinition[]
}

export const STEP_CATEGORIES = {
  NAVIGATION: 'Navigation',
  INTERACTIONS: 'Interactions',
  WAITING: 'Waiting',
  CREDENTIALS: 'Credentials',
  DATA_EXTRACTION: 'Data Extraction',
  AI_PROCESSING: 'AI Processing',
  API_INTEGRATION: 'API Integration'
} as const

export type StepCategoryKey = keyof typeof STEP_CATEGORIES
