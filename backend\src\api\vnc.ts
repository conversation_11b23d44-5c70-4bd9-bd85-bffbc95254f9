import { Router, Request, Response } from 'express';
import { ApiResponse } from '@rpa-project/shared';
import { asyncHandler } from '../middleware/errorHandler';
import { vncService, VNCServiceStatus } from '../services/vncService';

const router = Router();

// GET /api/vnc/status - Get VNC service status
router.get('/status', asyncHandler(async (req: Request, res: Response) => {
  const status = vncService.getStatus();
  
  const response: ApiResponse<VNCServiceStatus> = {
    success: true,
    data: status
  };
  
  res.json(response);
}));

// POST /api/vnc/stop - Force stop VNC services (admin endpoint)
router.post('/stop', asyncHandler(async (req: Request, res: Response) => {
  await vncService.forceStop();
  
  const response: ApiResponse = {
    success: true,
    message: 'VNC services stopped'
  };
  
  res.json(response);
}));

export default router;
