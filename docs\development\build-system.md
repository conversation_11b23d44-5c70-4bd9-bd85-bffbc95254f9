# Build System Documentation

## Översikt

RPA-projektet använder nu en förbättrad build-process med TypeScript composite builds och project references för bättre prestanda och utvecklarupplevelse.

## Arkitektur

### TypeScript Composite Builds

Projektet är strukturerat som ett monorepo med tre huvudkomponenter:

```
rpa-project/
├── tsconfig.json          # Root konfiguration med project references
├── shared/                # Delade typer och utilities
│   └── tsconfig.json     # Shared-specifik konfiguration
├── backend/              # Express server med Playwright
│   └── tsconfig.json     # Backend-specifik konfiguration
└── frontend/             # React frontend med Vite
    └── tsconfig.json     # Frontend-specifik konfiguration
```

### Project References

Root `tsconfig.json` definierar references till alla subprojekt:

```json
{
  "references": [
    { "path": "./shared" },
    { "path": "./backend" },
    { "path": "./frontend" }
  ]
}
```

Detta möjliggör:
- Inkrementell kompilering
- Bättre TypeScript-prestanda
- Automatisk dependency-hantering mellan projekt

## Tillgängliga Kommandon

### Build-kommandon

```bash
# Bygg alla komponenter i rätt ordning
npm run build

# Bygg individuella komponenter
npm run build:shared
npm run build:backend
npm run build:frontend
```

### Development-kommandon

```bash
# Starta alla komponenter i watch-läge parallellt
npm run dev

# Starta individuella komponenter
npm run dev:shared     # TypeScript watch mode
npm run dev:backend    # Nodemon för backend
npm run dev:frontend   # Vite dev server
```

### Maintenance-kommandon

```bash
# Rensa alla build-artefakter
npm run clean

# Rensa individuella komponenter
npm run clean:shared
npm run clean:backend
npm run clean:frontend

# TypeScript type-checking
npm run type-check           # Kör tsc --build
npm run type-check:watch     # Kör tsc --build --watch
```

## Build-ordning

Builds körs automatiskt i rätt ordning:

1. **Shared** - Byggs först eftersom andra projekt beror på det
2. **Backend** - Byggs efter shared
3. **Frontend** - Byggs sist

## Development Workflow

### Daglig utveckling

```bash
# Starta utvecklingsmiljön
npm run dev

# Detta startar:
# - Shared: TypeScript watch mode (port: ingen)
# - Backend: Nodemon server (port: 3002)
# - Frontend: Vite dev server (port: 3000)
```

### Efter ändringar i shared

När du gör ändringar i `shared/`-paketet:
- TypeScript watch mode kompilerar automatiskt
- Backend och frontend får automatiskt de nya typerna
- Ingen manuell restart behövs

### Type-checking

```bash
# Kontrollera alla TypeScript-fel
npm run type-check

# Kontinuerlig type-checking
npm run type-check:watch
```

## Prestanda-förbättringar

### Composite Builds

- **Inkrementell kompilering**: Endast ändrade filer kompileras
- **Parallell kompilering**: Oberoende projekt kan kompileras samtidigt
- **Bättre caching**: TypeScript cachar kompileringsresultat

### Project References

- **Automatisk dependency-resolution**: TypeScript förstår beroenden mellan projekt
- **Snabbare IDE-prestanda**: Bättre IntelliSense och error-checking
- **Optimerad build-pipeline**: Bygger endast vad som behövs

## Felsökning

### TypeScript-fel

```bash
# Kontrollera alla TypeScript-fel
npm run type-check

# Rensa och bygg om allt
npm run clean
npm run build
```

### Build-problem

```bash
# Rensa node_modules och installera om
npm run clean
rm -rf node_modules package-lock.json
npm install
npm run build
```

### Development-problem

```bash
# Starta om development-servrar
# Ctrl+C för att stoppa npm run dev
npm run dev
```

## Tekniska Detaljer

### TypeScript-konfiguration

- **Root**: Composite build med project references
- **Shared**: ESNext modules med declaration files
- **Backend**: CommonJS för Node.js-kompatibilitet
- **Frontend**: ESNext med Vite bundler resolution

### Dependencies

- **concurrently**: Kör flera npm scripts parallellt
- **rimraf**: Cross-platform fil/mapp-borttagning
- **typescript**: TypeScript compiler med composite build-stöd

## Migration från Tidigare System

### Vad som ändrats

1. **Root tsconfig.json**: Ny fil med project references
2. **Composite builds**: Alla tsconfig.json uppdaterade
3. **Build scripts**: Förbättrade med parallell körning
4. **Type-checking**: Använder nu `tsc --build`

### Bakåtkompatibilitet

- Alla befintliga kommandon fungerar fortfarande
- Inga ändringar i källkod krävs
- Samma portar och endpoints
