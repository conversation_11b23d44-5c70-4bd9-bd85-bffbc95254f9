import { RpaFlow, RpaStep, FlowSettings, ExecutionLog } from '@rpa-project/shared';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ontext, StepExecutionResult } from './IRunner';

/**
 * Abstract base class providing common functionality for runners
 */
export abstract class BaseRunner implements IRunner {
  protected logHandler: (log: Omit<ExecutionLog, 'timestamp'>) => void = () => {};
  protected cancellationChecker: (() => Promise<boolean>) | null = null;
  protected context?: RunnerContext;

  abstract initialize(settings: FlowSettings, variables: Record<string, any>): Promise<void>;
  abstract executeStep(step: RpaStep, context: RunnerContext, stepIndex?: number): Promise<StepExecutionResult>;
  abstract cleanup(): Promise<void>;
  abstract getSupportedStepTypes(): string[];

  setLogHandler(handler: (log: Omit<ExecutionLog, 'timestamp'>) => void): void {
    this.logHandler = handler;
  }

  setCancellationChecker(checker: () => Promise<boolean>): void {
    this.cancellationChecker = checker;
  }

  canHandleStep(stepType: string): boolean {
    return this.getSupportedStepTypes().includes(stepType);
  }

  /**
   * Default flow execution implementation
   * Can be overridden by specific runners if needed
   */
  async executeFlow(flow: RpaFlow, variables: Record<string, any> = {}): Promise<Record<string, any>> {
    await this.initialize(flow.settings || {}, variables);

    const context: RunnerContext = {
      variables: { ...variables },
      onLog: this.logHandler,
      cancellationChecker: this.cancellationChecker || undefined
    };

    for (let i = 0; i < flow.steps.length; i++) {
      const step = flow.steps[i];

      if (this.cancellationChecker && await this.cancellationChecker()) {
        throw new Error('Flow execution was cancelled');
      }

      if (!this.canHandleStep(step.type)) {
        throw new Error(`Runner cannot handle step type: ${step.type}`);
      }

      const result = await this.executeStep(step, context, i);

      if (!result.success) {
        throw new Error(result.error || `Step ${step.type} failed`);
      }

      // Update variables with any new values from the step
      if (result.variables) {
        Object.assign(context.variables, result.variables);
      }
    }

    await this.cleanup();
    return context.variables;
  }

  /**
   * Helper method to log messages
   */
  protected log(level: 'info' | 'warn' | 'error', message: string, stepId?: string): void {
    this.logHandler({
      level,
      message,
      stepId
    });
  }

  /**
   * Helper method to check for cancellation
   */
  protected async checkCancellation(): Promise<void> {
    if (this.cancellationChecker && await this.cancellationChecker()) {
      throw new Error('Execution was cancelled');
    }
  }

  /**
   * Helper method to interpolate variables in strings
   */
  protected interpolateVariables(text: string, variables: Record<string, any>): string {
    return text.replace(/\$\{([^}]+)\}/g, (match, varName) => {
      const value = variables[varName];
      return value !== undefined ? String(value) : match;
    });
  }
}
