# eAccounting API Integration - Teknisk Dokumentation

Detta dokument beskriver den tekniska implementationen av eAccounting API-integrationen i RPA-systemet.

## API Endpoints

### Base URL
```
https://eaccountingapi.vismaonline.com
```

### Autentisering
```http
Authorization: Bearer {access_token}
Content-Type: application/json
Accept: application/json
```

### Endpoints som används

1. **POST /v2/attachments** - Ladda upp filer
2. **POST /v2/vouchers** - Skapa verifikationer
3. **POST /v2/attachmentlinks** - Koppla filer till verifikationer

## Step Types

### 1. eAccountingUploadFile

Laddar upp en fil till eAccounting Attachments.

```typescript
interface EAccountingUploadFileStep extends RpaStepBase {
  type: 'eAccountingUploadFile';
  inputVariable: string; // Variable med base64 filinnehåll
  filename?: string; // Filnamn (max 200 tecken)
  contentType?: string; // MIME-typ
  comment?: string; // Kommentar
  variableName?: string; // Output variabel
}
```

**API Request:**
```json
{
  "ContentType": "application/pdf",
  "FileName": "faktura-123.pdf",
  "Data": "base64-encoded-content",
  "Comment": "Inköpsfaktura från leverantör"
}
```

**API Response:**
```json
{
  "Id": "abc123-def456-ghi789",
  "ContentType": "application/pdf",
  "FileName": "faktura-123.pdf",
  "Comment": "Inköpsfaktura från leverantör",
  "TemporaryUrl": "https://...",
  "UploadedBy": "<EMAIL>",
  "ImageDate": "2024-01-15T10:30:00Z",
  "AttachmentStatus": 1
}
```

### 2. eAccountingCreateVoucher

Skapar en verifikation med AI-assistans.

```typescript
interface EAccountingCreateVoucherStep extends RpaStepBase {
  type: 'eAccountingCreateVoucher';
  inputVariable: string; // Input data för AI
  aiPrompt: string; // AI-prompt
  voucherText?: string; // Verifikationstext (max 1000 tecken)
  voucherDate?: string; // Datum (YYYY-MM-DD)
  numberSeries?: string; // Nummerserie
  variableName?: string; // Output variabel
}
```

**AI System Prompt:**
```
Du är en expert på svensk bokföring och ska skapa verifikationsrader för eAccounting baserat på input-data.

VIKTIGT: Svara ENDAST med giltig JSON i följande format:
{
  "rows": [
    {
      "account": "kontonummer",
      "debit": belopp_eller_null,
      "credit": belopp_eller_null,
      "description": "beskrivning"
    }
  ],
  "transactionDate": "YYYY-MM-DD (om datum finns i input-data)",
  "explanation": "kort förklaring av verifikationen"
}
```

**API Request:**
```json
{
  "VoucherDate": "2024-01-15",
  "VoucherText": "Inköpsfaktura från leverantör",
  "Rows": [
    {
      "AccountNumber": 4010,
      "DebitAmount": 1000.00,
      "TransactionText": "Inköp av varor"
    },
    {
      "AccountNumber": 2640,
      "DebitAmount": 250.00,
      "TransactionText": "Ingående moms 25%"
    },
    {
      "AccountNumber": 2440,
      "CreditAmount": 1250.00,
      "TransactionText": "Leverantörsskuld"
    }
  ],
  "VoucherType": 2
}
```

### 3. eAccountingAttachFileToVoucher

Kopplar en uppladdad fil till en verifikation.

```typescript
interface EAccountingAttachFileToVoucherStep extends RpaStepBase {
  type: 'eAccountingAttachFileToVoucher';
  attachmentIdVariable: string; // Variabel med attachment ID
  voucherIdVariable: string; // Variabel med voucher ID
  variableName?: string; // Output variabel
}
```

**API Request:**
```json
{
  "DocumentId": "voucher-123",
  "DocumentType": 3,
  "AttachmentIds": ["abc123-def456-ghi789"]
}
```

### 4. eAccountingUploadAndCreateVoucher

Kombinerad operation som utför alla tre stegen ovan.

```typescript
interface EAccountingUploadAndCreateVoucherStep extends RpaStepBase {
  type: 'eAccountingUploadAndCreateVoucher';
  // Fil-egenskaper
  fileInputVariable: string;
  filename?: string;
  contentType?: string;
  fileComment?: string;
  // Verifikations-egenskaper
  voucherInputVariable: string;
  aiPrompt: string;
  voucherText?: string;
  voucherDate?: string;
  numberSeries?: string;
  variableName?: string;
}
```

## Datastrukturer

### VoucherRowApi
```typescript
interface EAccountingVoucherRow {
  AccountNumber: number; // Kontonummer (numeriskt)
  AccountDescription?: string; // Kontobeskrivning (readonly)
  DebitAmount?: number; // Debetbelopp (max 2 decimaler)
  CreditAmount?: number; // Kreditbelopp (max 2 decimaler)
  TransactionText?: string; // Transaktionstext (max 60 tecken)
  CostCenterItemId1?: string; // Kostnadsbärare 1
  CostCenterItemId2?: string; // Kostnadsbärare 2
  CostCenterItemId3?: string; // Kostnadsbärare 3
  VatCodeId?: string; // Moms-kod ID
  VatCodeAndPercent?: string; // Moms-kod och procent
  VatAmount?: number; // Momsbelopp
  ProjectId?: string; // Projekt-ID
}
```

### AttachmentLinkApi
```typescript
interface EAccountingAttachmentLink {
  DocumentId: string; // Dokument-ID
  DocumentType: number; // 3 = Voucher
  AttachmentIds: string[]; // Array av attachment ID:n
}
```

## Felhantering

### Vanliga API-fel

**400 Bad Request:**
```json
{
  "message": "Validation error.",
  "errors": {
    "FileName": {
      "errors": [
        {
          "errorCode": "E04500",
          "message": "FileName scale cannot be greater than 200 characters.",
          "value": "very-long-filename...",
          "developerHint": "Please check the format of your data."
        }
      ]
    }
  }
}
```

**401 Unauthorized:**
- Token har gått ut eller är ogiltigt
- Saknar rätt scopes (ea:accounting, ea:purchase)

**403 Forbidden:**
- Användaren har inte behörighet till funktionen
- Företaget har inte rätt licens

### Validering

**Filuppladdning:**
- Filnamn: Max 200 tecken, endast alfanumeriska + - _ ( )
- Content Type: image/jpeg, image/png, image/tiff, application/pdf
- Data: Måste vara giltig base64

**Verifikationer:**
- VoucherText: Max 1000 tecken
- VoucherDate: Format YYYY-MM-DD
- AccountNumber: Måste vara numeriskt
- DebitAmount/CreditAmount: Max 2 decimaler
- TransactionText: Max 60 tecken per rad

## Implementationsdetaljer

### Executor Context
```typescript
interface EAccountingExecutorContext {
  variables: Record<string, any>;
  onLog: (log: { level: 'info' | 'warn' | 'error'; message: string; stepId?: string }) => void;
  interpolateVariables: (text: string, variables: Record<string, any>) => string;
  customerId?: string;
}
```

### Token Management
```typescript
// Hämta eAccounting token för kund
const eAccountingTokens = await customerService.getCustomerTokensWithData(customerId);
const eAccountingToken = eAccountingTokens.find(token => 
  token.provider === 'eAccounting' && token.apiToken
);
```

### AI Integration
```typescript
// Använd LLMService för AI-processing
const completion = await LLMService.createChatCompletion([
  {
    role: 'system',
    content: systemPrompt
  },
  {
    role: 'user',
    content: userPrompt
  }
], {
  temperature: 0.1,
  maxTokens: 2000
});
```

### Variable Handling
```typescript
// Hantera både direkta variabelnamn och ${variableName} syntax
let actualVariableName = step.inputVariable;
const variableMatch = step.inputVariable.match(/^\$\{([^}]+)\}$/);
if (variableMatch) {
  actualVariableName = variableMatch[1];
}
```

## Registrering i Systemet

### Step Registry
```typescript
// backend/src/runners/registry/stepTypes.ts
export const STEP_RUNNER_MAPPING = {
  // ...
  eAccountingUploadFile: 'api',
  eAccountingCreateVoucher: 'api',
  eAccountingAttachFileToVoucher: 'api',
  eAccountingUploadAndCreateVoucher: 'api'
} as const;
```

### API Runner
```typescript
// backend/src/runners/api/APIRunner.ts
switch (stepType) {
  case 'eAccountingUploadFile':
    return await executeEAccountingUploadFile(step as any, executorContext, stepIndex);
  case 'eAccountingCreateVoucher':
    return await executeEAccountingCreateVoucher(step as any, executorContext, stepIndex);
  // ...
}
```

## Säkerhet

### OAuth2 Scopes
- `ea:api` - Grundläggande API-åtkomst
- `ea:accounting` - Åtkomst till bokföringsdata
- `ea:purchase` - Åtkomst till inköpsdata
- `offline_access` - Refresh token support

### Token Refresh
```typescript
// Tokens uppdateras automatiskt av customerService
// Refresh tokens är giltiga i 2 år
// Access tokens är giltiga i 1 timme
```

## Prestanda

### Rate Limits
- eAccounting API har rate limits
- Implementera retry-logik vid 429-fel
- Använd exponential backoff

### Optimering
- Kombinera operationer med `eAccountingUploadAndCreateVoucher`
- Cacha tokens mellan anrop
- Validera data innan API-anrop

## Testning

### Unit Tests
```typescript
// Testa varje executor separat
describe('eAccountingUploadFile', () => {
  it('should upload file successfully', async () => {
    // Mock API response
    // Test executor
    // Verify result
  });
});
```

### Integration Tests
```typescript
// Testa hela flödet
describe('eAccounting Integration', () => {
  it('should upload file and create voucher', async () => {
    // Test combined operation
  });
});
```

## Jämförelse med Fortnox

| Aspekt | eAccounting | Fortnox |
|--------|-------------|---------|
| Base URL | eaccountingapi.vismaonline.com | api.fortnox.se |
| Auth | OAuth2 Bearer | OAuth2 Bearer |
| File Upload | /v2/attachments | /3/archive |
| Vouchers | /v2/vouchers | /3/vouchers |
| File Links | /v2/attachmentlinks | /3/voucherfileconnections |
| Account Format | Numeriskt | Numeriskt |
| Date Format | YYYY-MM-DD | YYYY-MM-DD |
| Amount Fields | DebitAmount/CreditAmount | Debit/Credit |
| File ID Format | GUID | Numeriskt |
