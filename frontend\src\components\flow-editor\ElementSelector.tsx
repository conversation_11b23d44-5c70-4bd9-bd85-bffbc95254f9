import { useState } from 'react'

interface ElementSelectorProps {
  value: string
  onChange: (selector: string) => void
  placeholder?: string
  style?: React.CSSProperties
}

type SelectorType = 'css' | 'id' | 'class' | 'attribute' | 'xpath' | 'text'

interface SelectorConfig {
  type: SelectorType
  value: string
  attributeName?: string
  attributeValue?: string
}

export function ElementSelector({ value, onChange, placeholder, style }: ElementSelectorProps) {
  const [selectorConfig, setSelectorConfig] = useState<SelectorConfig>(() => {
    // Parse existing selector to determine type and value
    if (value.startsWith('#')) {
      return { type: 'id', value: value.slice(1) }
    } else if (value.startsWith('.')) {
      return { type: 'class', value: value.slice(1) }
    } else if (value.startsWith('xpath=')) {
      return { type: 'xpath', value: value.slice(6) }
    } else if (value.startsWith('text=')) {
      return { type: 'text', value: value.slice(5) }
    } else if (value.includes('[') && value.includes('=')) {
      // Parse attribute selector like [name="field"] or input[type="text"]
      const match = value.match(/\[([^=]+)=["']?([^"'\]]+)["']?\]/)
      if (match) {
        return { 
          type: 'attribute', 
          value: value.replace(/\[[^\]]+\]/, '').trim() || '*',
          attributeName: match[1],
          attributeValue: match[2]
        }
      }
    }
    
    return { type: 'css', value: value || '' }
  })

  const updateSelector = (config: SelectorConfig) => {
    setSelectorConfig(config)
    
    let selector = ''
    switch (config.type) {
      case 'id':
        selector = config.value ? `#${config.value}` : ''
        break
      case 'class':
        selector = config.value ? `.${config.value}` : ''
        break
      case 'attribute':
        if (config.attributeName && config.attributeValue) {
          const element = config.value && config.value !== '*' ? config.value : ''
          selector = `${element}[${config.attributeName}="${config.attributeValue}"]`
        }
        break
      case 'xpath':
        selector = config.value ? `xpath=${config.value}` : ''
        break
      case 'text':
        selector = config.value ? `text=${config.value}` : ''
        break
      case 'css':
      default:
        selector = config.value
        break
    }
    
    onChange(selector)
  }

  const inputStyle: React.CSSProperties = {
    padding: '0.5rem',
    border: '1px solid #d1d5db',
    borderRadius: '0.375rem',
    fontSize: '0.875rem',
    width: '100%',
    ...style
  }

  const selectStyle: React.CSSProperties = {
    ...inputStyle,
    marginBottom: '0.5rem'
  }

  const smallInputStyle: React.CSSProperties = {
    ...inputStyle,
    marginBottom: '0.25rem'
  }

  return (
    <div>
      <select
        style={selectStyle}
        value={selectorConfig.type}
        onChange={(e) => updateSelector({ ...selectorConfig, type: e.target.value as SelectorType })}
      >
        <option value="css">CSS Selector</option>
        <option value="id">Element ID</option>
        <option value="class">CSS Class</option>
        <option value="attribute">Attribut</option>
        <option value="xpath">XPath</option>
        <option value="text">Text Content</option>
      </select>

      {selectorConfig.type === 'id' && (
        <input
          type="text"
          style={inputStyle}
          value={selectorConfig.value}
          onChange={(e) => updateSelector({ ...selectorConfig, value: e.target.value })}
          placeholder="myElementId"
        />
      )}

      {selectorConfig.type === 'class' && (
        <input
          type="text"
          style={inputStyle}
          value={selectorConfig.value}
          onChange={(e) => updateSelector({ ...selectorConfig, value: e.target.value })}
          placeholder="my-css-class"
        />
      )}

      {selectorConfig.type === 'attribute' && (
        <>
          <input
            type="text"
            style={smallInputStyle}
            value={selectorConfig.value || ''}
            onChange={(e) => updateSelector({ ...selectorConfig, value: e.target.value })}
            placeholder="Element (t.ex. input, button, eller lämna tom)"
          />
          <div style={{ display: 'flex', gap: '0.5rem' }}>
            <input
              type="text"
              style={{ ...smallInputStyle, flex: 1 }}
              value={selectorConfig.attributeName || ''}
              onChange={(e) => updateSelector({ ...selectorConfig, attributeName: e.target.value })}
              placeholder="Attributnamn (t.ex. name, data-id)"
            />
            <input
              type="text"
              style={{ ...smallInputStyle, flex: 1 }}
              value={selectorConfig.attributeValue || ''}
              onChange={(e) => updateSelector({ ...selectorConfig, attributeValue: e.target.value })}
              placeholder="Attributvärde"
            />
          </div>
        </>
      )}

      {selectorConfig.type === 'xpath' && (
        <input
          type="text"
          style={inputStyle}
          value={selectorConfig.value}
          onChange={(e) => updateSelector({ ...selectorConfig, value: e.target.value })}
          placeholder="//button[@id='submit']"
        />
      )}

      {selectorConfig.type === 'text' && (
        <input
          type="text"
          style={inputStyle}
          value={selectorConfig.value}
          onChange={(e) => updateSelector({ ...selectorConfig, value: e.target.value })}
          placeholder="Klicka här"
        />
      )}

      {selectorConfig.type === 'css' && (
        <input
          type="text"
          style={inputStyle}
          value={selectorConfig.value}
          onChange={(e) => updateSelector({ ...selectorConfig, value: e.target.value })}
          placeholder={placeholder || "button, #id, .class, [name='field']"}
        />
      )}
    </div>
  )
}
