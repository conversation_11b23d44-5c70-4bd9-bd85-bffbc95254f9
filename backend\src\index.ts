import dotenv from 'dotenv';

// Load environment variables first
dotenv.config();

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import https from 'https';
import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { flowRoutes } from './api/flows';
import { executionRoutes } from './api/executions';
import { queueRoutes } from './api/queue';
import { scheduleRoutes } from './api/schedules';
import customerRoutes from './api/customers';
import { credentialRoutes } from './api/credentials';
import { settingsRoutes } from './api/settings';
import { aiAssistantRoutes } from './api/ai-assistant';
import oauth2Routes from './api/oauth2';
import vncRoutes from './api/vnc';
import { errorHandler } from './middleware/errorHandler';
import { initializeQueue } from './queue/queue';
import { initializeScheduler, shutdownScheduler } from './queue/scheduler';
import { tokenRefreshJob } from './jobs/tokenRefreshJob';
import { vncService } from './services/vncService';
// Import database to initialize it
import './database/database';
// Initialize runner factory
import { getRunnerFactory } from './runners/factory';

const app = express();
const PORT = process.env.PORT || 44300;
const USE_HTTPS = process.env.USE_HTTPS !== 'false'; // Default to HTTPS

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// API routes
app.use('/api/flows', flowRoutes);
app.use('/api/executions', executionRoutes);
app.use('/api/queue', queueRoutes);
app.use('/api/schedules', scheduleRoutes);
app.use('/api/customers', customerRoutes);
app.use('/api/credentials', credentialRoutes);
app.use('/api/settings', settingsRoutes);
app.use('/api/ai-assistant', aiAssistantRoutes);
app.use('/api/oauth2', oauth2Routes);
app.use('/api/vnc', vncRoutes);

// Error handling
app.use(errorHandler);

// 404 handler
app.use('/*path', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route not found'
  });
});

async function startServer() {
  try {
    // Database is already initialized via import
    console.log('✅ SQLite database initialized');

    // Initialize runner factory
    const runnerFactory = getRunnerFactory();
    console.log('✅ Runner factory initialized');

    // Initialize queue system
    await initializeQueue();
    console.log('✅ Queue system initialized');

    // Initialize scheduler
    await initializeScheduler();
    console.log('✅ Scheduler initialized');

    // Start token refresh job
    tokenRefreshJob.start();
    console.log('✅ Token refresh job started');

    // Start server (HTTP or HTTPS)
    console.log(`🔧 USE_HTTPS environment variable: ${process.env.USE_HTTPS}`);
    console.log(`🔧 USE_HTTPS resolved value: ${USE_HTTPS}`);

    if (USE_HTTPS) {
      console.log('🔐 Starting HTTPS server...');
      startHTTPSServer();
    } else {
      console.log('🌐 Starting HTTP server...');
      startHTTPServer();
    }
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// SSL Certificate functions
function createDevelopmentCertificatesWithOpenSSL(): boolean {
  const certsDir = path.join(__dirname, '../../certs');
  const keyPath = path.join(certsDir, 'localhost-key.pem');
  const certPath = path.join(certsDir, 'localhost-cert.pem');

  try {
    // Create certs directory if it doesn't exist
    if (!fs.existsSync(certsDir)) {
      fs.mkdirSync(certsDir, { recursive: true });
    }

    console.log('🔐 Generating SSL certificates with OpenSSL...');

    // Generate private key
    execSync(`openssl genrsa -out "${keyPath}" 2048`, { stdio: 'pipe' });
    console.log('🔑 Private key generated');

    // Generate certificate
    const opensslCmd = `openssl req -new -x509 -key "${keyPath}" -out "${certPath}" -days 365 -subj "/C=SE/ST=Stockholm/L=Stockholm/O=Development/OU=RPA/CN=localhost"`;
    execSync(opensslCmd, { stdio: 'pipe' });
    console.log('📜 Certificate generated');

    return true;
  } catch (error: any) {
    console.error('❌ Error generating certificates with OpenSSL:', error.message);
    return false;
  }
}

function getSSLOptions(): { key: Buffer; cert: Buffer } | null {
  const certsDir = path.join(__dirname, '../../certs');
  const keyPath = path.join(certsDir, 'localhost-key.pem');
  const certPath = path.join(certsDir, 'localhost-cert.pem');

  console.log(`🔍 Looking for certificates in: ${certsDir}`);
  console.log(`🔍 Key path: ${keyPath}`);
  console.log(`🔍 Cert path: ${certPath}`);

  // Try to load existing certificates first
  if (fs.existsSync(keyPath) && fs.existsSync(certPath)) {
    console.log('📜 Using existing SSL certificates from certs/ directory');
    try {
      return {
        key: fs.readFileSync(keyPath),
        cert: fs.readFileSync(certPath)
      };
    } catch (error) {
      console.error('❌ Error reading existing certificates:', error);
    }
  }

  // Try to generate new certificates with OpenSSL
  console.log('📜 Generating new SSL certificates with OpenSSL...');
  if (createDevelopmentCertificatesWithOpenSSL()) {
    try {
      return {
        key: fs.readFileSync(keyPath),
        cert: fs.readFileSync(certPath)
      };
    } catch (error) {
      console.error('❌ Error reading generated certificates:', error);
    }
  }

  console.error('❌ Could not create or load SSL certificates');
  return null;
}

function startHTTPSServer(): void {
  try {
    console.log('🔐 Loading SSL certificates...');
    const sslOptions = getSSLOptions();
    if (!sslOptions) {
      console.error('❌ Could not load SSL certificates, falling back to HTTP');
      startHTTPServer();
      return;
    }

    console.log('✅ SSL certificates loaded successfully');
    const server = https.createServer(sslOptions, app);

    server.listen(PORT, () => {
      console.log(`🚀 HTTPS Server running on port ${PORT}`);
      console.log(`🔐 HTTPS Health check: https://localhost:${PORT}/health`);
      console.log(`🔐 HTTPS API docs: https://localhost:${PORT}/api`);
      console.log(`📞 OAuth2 Callback: https://localhost:${PORT}/api/oauth2/callback/eEkonomi`);
      console.log(`💾 Database: SQLite (persistent storage)`);
      console.log('');
      console.log('⚠️  Note: You may need to accept the self-signed certificate in your browser.');
    });

    server.on('error', (error: any) => {
      console.error('❌ HTTPS Server error:', error.message);
      if (error.code === 'EADDRINUSE') {
        console.log(`💡 Port ${PORT} is already in use. Please stop any other services using this port.`);
      }
      process.exit(1);
    });

  } catch (error) {
    console.error('❌ Failed to start HTTPS server:', error);
    process.exit(1);
  }
}

function startHTTPServer(): void {
  app.listen(PORT, (error?: Error) => {
    if (error) {
      console.error('❌ Failed to start server:', error);
      process.exit(1);
    }
    console.log(`🚀 HTTP Server running on port ${PORT}`);
    console.log(`📊 Health check: http://localhost:${PORT}/health`);
    console.log(`📖 API docs: http://localhost:${PORT}/api`);
    console.log(`📞 OAuth2 Callback: http://localhost:${PORT}/api/oauth2/callback/eEkonomi`);
    console.log(`💾 Database: SQLite (persistent storage)`);
    console.log('');
    console.log('💡 To enable HTTPS, set USE_HTTPS=true in environment variables.');
  });
}

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('🛑 SIGTERM received, shutting down gracefully');
  tokenRefreshJob.stop();
  await shutdownScheduler();
  await vncService.forceStop();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('🛑 SIGINT received, shutting down gracefully');
  tokenRefreshJob.stop();
  await shutdownScheduler();
  await vncService.forceStop();
  process.exit(0);
});

startServer();
