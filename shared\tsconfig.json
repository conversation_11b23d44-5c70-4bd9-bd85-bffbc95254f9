{"extends": "../tsconfig.json", "compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "node", "lib": ["ES2020", "DOM"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "composite": true, "declaration": true, "declarationMap": true, "sourceMap": true, "resolveJsonModule": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}