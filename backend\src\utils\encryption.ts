import crypto from 'crypto';
import fs from 'fs';
import path from 'path';

// Simple base32 implementation
const base32Alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';

function base32Decode(input: string): Uint8Array {
  const cleanInput = input.replace(/\s/g, '').toUpperCase();
  const output = new Uint8Array(Math.floor(cleanInput.length * 5 / 8));
  let bits = 0;
  let value = 0;
  let index = 0;

  for (let i = 0; i < cleanInput.length; i++) {
    const char = cleanInput[i];
    const charIndex = base32Alphabet.indexOf(char);
    if (charIndex === -1) {
      throw new Error(`Invalid base32 character: ${char}`);
    }

    value = (value << 5) | charIndex;
    bits += 5;

    if (bits >= 8) {
      output[index++] = (value >>> (bits - 8)) & 255;
      bits -= 8;
    }
  }

  return output.slice(0, index);
}

const KEY_LENGTH = 32; // 256 bits
const IV_LENGTH = 16; // 128 bits

// Path to store the master key
const MASTER_KEY_PATH = process.env.MASTER_KEY_PATH || path.join(process.cwd(), '..', 'data', 'master.key');

/**
 * Generate a new master key
 */
export function generateMasterKey(): Buffer {
  return crypto.randomBytes(KEY_LENGTH);
}

/**
 * Save master key to file
 */
export function saveMasterKey(key: Buffer): void {
  const keyDir = path.dirname(MASTER_KEY_PATH);
  if (!fs.existsSync(keyDir)) {
    fs.mkdirSync(keyDir, { recursive: true });
  }
  fs.writeFileSync(MASTER_KEY_PATH, key);
  // Set restrictive permissions (owner read/write only)
  fs.chmodSync(MASTER_KEY_PATH, 0o600);
}

/**
 * Load master key from file
 */
export function loadMasterKey(): Buffer | null {
  try {
    if (!fs.existsSync(MASTER_KEY_PATH)) {
      return null;
    }
    return fs.readFileSync(MASTER_KEY_PATH);
  } catch (error) {
    console.error('Error loading master key:', error);
    return null;
  }
}

/**
 * Get or create master key
 */
export function getMasterKey(): Buffer {
  let key = loadMasterKey();
  if (!key) {
    console.log('🔑 Generating new master key...');
    key = generateMasterKey();
    saveMasterKey(key);
    console.log(`🔑 Master key saved to: ${MASTER_KEY_PATH}`);
  }
  return key;
}

/**
 * Set a custom master key (for configuration)
 */
export function setMasterKey(keyHex: string): void {
  const key = Buffer.from(keyHex, 'hex');
  if (key.length !== KEY_LENGTH) {
    throw new Error(`Master key must be ${KEY_LENGTH} bytes (${KEY_LENGTH * 2} hex characters)`);
  }
  saveMasterKey(key);
  console.log('🔑 Master key updated');
}

/**
 * Encrypt a string using the master key
 */
export function encrypt(plaintext: string): string {
  const key = getMasterKey();
  const iv = crypto.randomBytes(IV_LENGTH);

  const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);

  let encrypted = cipher.update(plaintext, 'utf8', 'hex');
  encrypted += cipher.final('hex');

  // Combine IV + encrypted data
  const combined = Buffer.concat([iv, Buffer.from(encrypted, 'hex')]);
  return combined.toString('base64');
}

/**
 * Decrypt a string using the master key
 */
export function decrypt(encryptedData: string): string {
  const key = getMasterKey();
  const combined = Buffer.from(encryptedData, 'base64');

  // Extract IV and encrypted data
  const iv = combined.subarray(0, IV_LENGTH);
  const encrypted = combined.subarray(IV_LENGTH);

  const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);

  let decrypted = decipher.update(encrypted, undefined, 'utf8');
  decrypted += decipher.final('utf8');

  return decrypted;
}

/**
 * Generate TOTP code from secret
 */
export function generateTOTP(secret: string, timeStep: number = 30): string {
  const time = Math.floor(Date.now() / 1000 / timeStep);
  const timeBuffer = Buffer.alloc(8);
  timeBuffer.writeBigUInt64BE(BigInt(time));
  
  const secretBuffer = Buffer.from(base32Decode(secret));
  const hmac = crypto.createHmac('sha1', secretBuffer);
  hmac.update(timeBuffer);
  const hash = hmac.digest();
  
  const offset = hash[hash.length - 1] & 0x0f;
  const code = (
    ((hash[offset] & 0x7f) << 24) |
    ((hash[offset + 1] & 0xff) << 16) |
    ((hash[offset + 2] & 0xff) << 8) |
    (hash[offset + 3] & 0xff)
  ) % 1000000;
  
  return code.toString().padStart(6, '0');
}

/**
 * Validate base32 secret for TOTP
 */
export function validateBase32Secret(secret: string): boolean {
  try {
    // Remove spaces and convert to uppercase
    const cleanSecret = secret.replace(/\s/g, '').toUpperCase();
    // Check if it's valid base32
    base32Decode(cleanSecret);
    return true;
  } catch {
    return false;
  }
}
