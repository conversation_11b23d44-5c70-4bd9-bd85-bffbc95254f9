import { LL<PERSON>rovider, ChatCompletionRequest, ChatCompletionResponse } from './interfaces';

export abstract class Base<PERSON><PERSON><PERSON>ider implements LLMProvider {
  abstract name: string;
  
  abstract isConfigured(): boolean;
  abstract getSupportedModels(): string[];
  abstract createChatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse>;
  
  async testConnection(): Promise<boolean> {
    try {
      console.log(`🧪 Testing ${this.name} provider connection...`);
      const supportedModels = this.getSupportedModels();
      console.log(`📋 Supported models: ${supportedModels.join(', ')}`);

      if (supportedModels.length === 0) {
        console.log('❌ No supported models found');
        return false;
      }

      const testModel = supportedModels[0];
      console.log(`🎯 Testing with model: ${testModel}`);

      await this.createChatCompletion({
        model: testModel,
        messages: [{ role: 'user', content: 'test' }],
        maxTokens: 1
      });

      console.log(`✅ ${this.name} provider test successful`);
      return true;
    } catch (error) {
      console.log(`❌ ${this.name} provider test failed:`, error instanceof Error ? error.message : error);
      return false;
    }
  }
}
