# eAccounting - Exempel Flöden

Detta dokument innehåller kompletta exempel på RPA-flöden som använder eAccounting API-integration.

## Exempel 1: En<PERSON>

```json
{
  "name": "eAccounting - <PERSON><PERSON>",
  "description": "Ladda ner faktura från leverantörsportal och skapa verifikation i eAccounting",
  "steps": [
    {
      "id": "step-1",
      "type": "navigate",
      "name": "Navigera till leverantörsportal",
      "description": "Gå till leverantörens fakturaportall",
      "url": "https://portal.supplier.com/invoices"
    },
    {
      "id": "step-2",
      "type": "fillPassword",
      "name": "Logga in",
      "description": "Logga in med sparade credentials",
      "usernameSelector": "#username",
      "passwordSelector": "#password",
      "credentialId": "supplier-portal-login"
    },
    {
      "id": "step-3",
      "type": "extractText",
      "name": "Hämta fakturanummer",
      "description": "Extrahera fakturanummer från sidan",
      "selector": ".invoice-number",
      "variableName": "invoice-number"
    },
    {
      "id": "step-4",
      "type": "extractText",
      "name": "Hämta leverantörsnamn",
      "description": "Extrahera leverantörsnamn",
      "selector": ".supplier-name",
      "variableName": "supplier-name"
    },
    {
      "id": "step-5",
      "type": "extractText",
      "name": "Hämta totalbelopp",
      "description": "Extrahera fakturans totalbelopp",
      "selector": ".total-amount",
      "variableName": "total-amount"
    },
    {
      "id": "step-6",
      "type": "downloadFile",
      "name": "Ladda ner faktura-PDF",
      "description": "Ladda ner fakturan som PDF",
      "triggerSelector": ".download-pdf-btn",
      "variableName": "invoice-pdf",
      "saveToFile": false
    },
    {
      "id": "step-7",
      "type": "eAccountingUploadAndCreateVoucher",
      "name": "Skapa verifikation med bifogad faktura",
      "description": "Ladda upp faktura och skapa verifikation i eAccounting",
      "fileInputVariable": "invoice-pdf",
      "filename": "faktura-${supplier-name}-${invoice-number}.pdf",
      "contentType": "application/pdf",
      "fileComment": "Inköpsfaktura ${invoice-number} från ${supplier-name}",
      "voucherInputVariable": "total-amount",
      "aiPrompt": "Skapa en inköpsverifikation för faktura ${invoice-number} från ${supplier-name}. Totalbelopp: ${total-amount} SEK inklusive 25% moms. Använd konto 4010 för inköp, 2640 för ingående moms och 2440 för leverantörsskuld.",
      "voucherText": "Inköpsfaktura ${invoice-number} - ${supplier-name}",
      "variableName": "voucher-result"
    },
    {
      "id": "step-8",
      "type": "takeScreenshot",
      "name": "Ta skärmdump av resultat",
      "description": "Dokumentera slutresultatet",
      "variableName": "completion-screenshot"
    }
  ]
}
```

## Exempel 2: Kvittohantering med Separata Steg

```json
{
  "name": "eAccounting - Kvittohantering",
  "description": "Hantera kvitton med separata steg för uppladdning och verifikationsskapande",
  "steps": [
    {
      "id": "step-1",
      "type": "navigate",
      "name": "Gå till kvittoportal",
      "description": "Navigera till kvittoportalen",
      "url": "https://receipts.company.com"
    },
    {
      "id": "step-2",
      "type": "click",
      "name": "Välj kvitto",
      "description": "Klicka på första kvittot i listan",
      "selector": ".receipt-item:first-child"
    },
    {
      "id": "step-3",
      "type": "extractText",
      "name": "Hämta kvittobelopp",
      "description": "Extrahera beloppet från kvittot",
      "selector": ".receipt-amount",
      "variableName": "receipt-amount"
    },
    {
      "id": "step-4",
      "type": "extractText",
      "name": "Hämta butikens namn",
      "description": "Extrahera butiksnamnet",
      "selector": ".store-name",
      "variableName": "store-name"
    },
    {
      "id": "step-5",
      "type": "extractText",
      "name": "Hämta datum",
      "description": "Extrahera kvittodatum",
      "selector": ".receipt-date",
      "variableName": "receipt-date"
    },
    {
      "id": "step-6",
      "type": "downloadFile",
      "name": "Ladda ner kvitto",
      "description": "Ladda ner kvittobilden",
      "triggerSelector": ".download-receipt",
      "variableName": "receipt-image",
      "saveToFile": false
    },
    {
      "id": "step-7",
      "type": "eAccountingUploadFile",
      "name": "Ladda upp kvitto till eAccounting",
      "description": "Ladda upp kvittobilden",
      "inputVariable": "receipt-image",
      "filename": "kvitto-${store-name}-${receipt-date}.jpg",
      "contentType": "image/jpeg",
      "comment": "Kvitto från ${store-name} datum ${receipt-date}",
      "variableName": "uploaded-receipt"
    },
    {
      "id": "step-8",
      "type": "eAccountingCreateVoucher",
      "name": "Skapa utgiftsverifikation",
      "description": "Skapa verifikation för kvittot",
      "inputVariable": "receipt-amount",
      "aiPrompt": "Skapa en utgiftsverifikation för kvitto från ${store-name}. Belopp: ${receipt-amount} SEK. Använd konto 6250 för kontorsmaterial och 2440 för leverantörsskuld. Om beloppet inkluderar moms, använd konto 2640 för ingående moms.",
      "voucherText": "Utgift ${store-name} - ${receipt-date}",
      "voucherDate": "${receipt-date}",
      "variableName": "voucher-created"
    },
    {
      "id": "step-9",
      "type": "eAccountingAttachFileToVoucher",
      "name": "Koppla kvitto till verifikation",
      "description": "Bifoga kvittobilden till verifikationen",
      "attachmentIdVariable": "uploaded-receipt",
      "voucherIdVariable": "voucher-created",
      "variableName": "attachment-result"
    }
  ]
}
```

## Exempel 3: Bankutdrag med Flera Transaktioner

```json
{
  "name": "eAccounting - Bankutdragshantering",
  "description": "Hantera bankutdrag med flera transaktioner",
  "steps": [
    {
      "id": "step-1",
      "type": "navigate",
      "name": "Gå till bankens webbsida",
      "description": "Navigera till bankens portal",
      "url": "https://bank.se/business"
    },
    {
      "id": "step-2",
      "type": "fillPassword",
      "name": "Logga in på banken",
      "description": "Logga in med BankID",
      "usernameSelector": "#personnummer",
      "passwordSelector": "#password",
      "credentialId": "bank-login"
    },
    {
      "id": "step-3",
      "type": "click",
      "name": "Välj företagskonto",
      "description": "Klicka på företagskontot",
      "selector": ".business-account"
    },
    {
      "id": "step-4",
      "type": "downloadFile",
      "name": "Ladda ner bankutdrag",
      "description": "Ladda ner bankutdrag som PDF",
      "triggerSelector": ".download-statement",
      "variableName": "bank-statement",
      "saveToFile": false
    },
    {
      "id": "step-5",
      "type": "extractPdfValues",
      "name": "Extrahera transaktioner från PDF",
      "description": "Använd AI för att extrahera transaktionsdata",
      "inputVariable": "bank-statement",
      "aiPrompt": "Extrahera alla transaktioner från detta bankutdrag. För varje transaktion, ange datum, beskrivning, belopp och om det är en in- eller utbetalning.",
      "variableName": "transactions-data"
    },
    {
      "id": "step-6",
      "type": "eAccountingUploadFile",
      "name": "Ladda upp bankutdrag",
      "description": "Ladda upp bankutdraget till eAccounting",
      "inputVariable": "bank-statement",
      "filename": "bankutdrag-${current-date}.pdf",
      "contentType": "application/pdf",
      "comment": "Bankutdrag för månaden",
      "variableName": "uploaded-statement"
    },
    {
      "id": "step-7",
      "type": "eAccountingCreateVoucher",
      "name": "Skapa bankverifikation",
      "description": "Skapa verifikation för banktransaktioner",
      "inputVariable": "transactions-data",
      "aiPrompt": "Skapa en bankverifikation baserat på dessa transaktioner. Använd konto 1930 för bankkonto. För utgifter, gissa lämpliga kostnadskonton (6xxx-serien). För inkomster, använd lämpliga intäktskonton (3xxx-serien). Gruppera liknande transaktioner.",
      "voucherText": "Banktransaktioner enligt utdrag",
      "variableName": "bank-voucher"
    },
    {
      "id": "step-8",
      "type": "eAccountingAttachFileToVoucher",
      "name": "Bifoga bankutdrag",
      "description": "Koppla bankutdraget till verifikationen",
      "attachmentIdVariable": "uploaded-statement",
      "voucherIdVariable": "bank-voucher",
      "variableName": "final-result"
    }
  ]
}
```

## Exempel 4: Reseräkning med Kvitton

```json
{
  "name": "eAccounting - Reseräkningshantering",
  "description": "Hantera reseräkning med flera kvitton",
  "steps": [
    {
      "id": "step-1",
      "type": "navigate",
      "name": "Gå till reseräkningssystem",
      "description": "Navigera till företagets reseräkningssystem",
      "url": "https://expenses.company.com"
    },
    {
      "id": "step-2",
      "type": "fillPassword",
      "name": "Logga in",
      "description": "Logga in i systemet",
      "usernameSelector": "#email",
      "passwordSelector": "#password",
      "credentialId": "expense-system-login"
    },
    {
      "id": "step-3",
      "type": "click",
      "name": "Välj reseräkning",
      "description": "Klicka på första reseräkningen",
      "selector": ".expense-report:first-child"
    },
    {
      "id": "step-4",
      "type": "extractText",
      "name": "Hämta anställdas namn",
      "description": "Extrahera vem som gjort reseräkningen",
      "selector": ".employee-name",
      "variableName": "employee-name"
    },
    {
      "id": "step-5",
      "type": "extractText",
      "name": "Hämta totalt belopp",
      "description": "Extrahera totalt belopp för resan",
      "selector": ".total-amount",
      "variableName": "total-amount"
    },
    {
      "id": "step-6",
      "type": "extractText",
      "name": "Hämta resebeskrivning",
      "description": "Extrahera beskrivning av resan",
      "selector": ".trip-description",
      "variableName": "trip-description"
    },
    {
      "id": "step-7",
      "type": "downloadFile",
      "name": "Ladda ner sammanställning",
      "description": "Ladda ner reseräkningssammanställning",
      "triggerSelector": ".download-summary",
      "variableName": "expense-summary",
      "saveToFile": false
    },
    {
      "id": "step-8",
      "type": "downloadFile",
      "name": "Ladda ner alla kvitton",
      "description": "Ladda ner ZIP-fil med alla kvitton",
      "triggerSelector": ".download-receipts",
      "variableName": "receipts-zip",
      "saveToFile": false
    },
    {
      "id": "step-9",
      "type": "eAccountingUploadFile",
      "name": "Ladda upp sammanställning",
      "description": "Ladda upp reseräkningssammanställningen",
      "inputVariable": "expense-summary",
      "filename": "reserakning-${employee-name}-${current-date}.pdf",
      "contentType": "application/pdf",
      "comment": "Reseräkning för ${employee-name}: ${trip-description}",
      "variableName": "uploaded-summary"
    },
    {
      "id": "step-10",
      "type": "eAccountingUploadFile",
      "name": "Ladda upp kvitton",
      "description": "Ladda upp kvittona",
      "inputVariable": "receipts-zip",
      "filename": "kvitton-${employee-name}-${current-date}.zip",
      "comment": "Kvitton för reseräkning ${employee-name}",
      "variableName": "uploaded-receipts"
    },
    {
      "id": "step-11",
      "type": "eAccountingCreateVoucher",
      "name": "Skapa reseräkningsverifikation",
      "description": "Skapa verifikation för reseräkningen",
      "inputVariable": "total-amount",
      "aiPrompt": "Skapa en verifikation för reseräkning från ${employee-name}. Totalt belopp: ${total-amount} SEK. Resa: ${trip-description}. Använd konto 6250 för resekostnader, 2640 för ingående moms (om tillämpligt) och 2890 för personalens utlägg.",
      "voucherText": "Reseräkning ${employee-name} - ${trip-description}",
      "variableName": "expense-voucher"
    },
    {
      "id": "step-12",
      "type": "eAccountingAttachFileToVoucher",
      "name": "Bifoga sammanställning",
      "description": "Koppla sammanställningen till verifikationen",
      "attachmentIdVariable": "uploaded-summary",
      "voucherIdVariable": "expense-voucher",
      "variableName": "summary-attached"
    },
    {
      "id": "step-13",
      "type": "eAccountingAttachFileToVoucher",
      "name": "Bifoga kvitton",
      "description": "Koppla kvittona till verifikationen",
      "attachmentIdVariable": "uploaded-receipts",
      "voucherIdVariable": "expense-voucher",
      "variableName": "receipts-attached"
    }
  ]
}
```

## Exempel 5: Månadsavslut med Flera Dokument

```json
{
  "name": "eAccounting - Månadsavslut",
  "description": "Automatiserat månadsavslut med uppladdning av flera dokument",
  "steps": [
    {
      "id": "step-1",
      "type": "navigate",
      "name": "Gå till ekonomisystem",
      "description": "Navigera till huvudekonomisystemet",
      "url": "https://erp.company.com/month-end"
    },
    {
      "id": "step-2",
      "type": "fillPassword",
      "name": "Logga in",
      "description": "Logga in i ERP-systemet",
      "usernameSelector": "#username",
      "passwordSelector": "#password",
      "credentialId": "erp-login"
    },
    {
      "id": "step-3",
      "type": "downloadFile",
      "name": "Ladda ner löneunderlag",
      "description": "Ladda ner löneunderlag för månaden",
      "triggerSelector": ".download-payroll",
      "variableName": "payroll-data",
      "saveToFile": false
    },
    {
      "id": "step-4",
      "type": "downloadFile",
      "name": "Ladda ner avskrivningsunderlag",
      "description": "Ladda ner avskrivningsunderlag",
      "triggerSelector": ".download-depreciation",
      "variableName": "depreciation-data",
      "saveToFile": false
    },
    {
      "id": "step-5",
      "type": "downloadFile",
      "name": "Ladda ner periodiseringar",
      "description": "Ladda ner periodiseringsunderlag",
      "triggerSelector": ".download-accruals",
      "variableName": "accruals-data",
      "saveToFile": false
    },
    {
      "id": "step-6",
      "type": "eAccountingUploadAndCreateVoucher",
      "name": "Skapa löneverifikation",
      "description": "Ladda upp och skapa verifikation för löner",
      "fileInputVariable": "payroll-data",
      "filename": "loneunderlag-${current-month}.pdf",
      "contentType": "application/pdf",
      "fileComment": "Löneunderlag för ${current-month}",
      "voucherInputVariable": "payroll-data",
      "aiPrompt": "Skapa löneverifikation baserat på löneunderlaget. Använd konto 7010 för löner, 7510 för sociala avgifter, 2710 för personalskatt, 2730 för sociala avgifter och 1630 för lönekonto.",
      "voucherText": "Löner ${current-month}",
      "variableName": "payroll-voucher"
    },
    {
      "id": "step-7",
      "type": "eAccountingUploadAndCreateVoucher",
      "name": "Skapa avskrivningsverifikation",
      "description": "Ladda upp och skapa verifikation för avskrivningar",
      "fileInputVariable": "depreciation-data",
      "filename": "avskrivningar-${current-month}.pdf",
      "contentType": "application/pdf",
      "fileComment": "Avskrivningsunderlag för ${current-month}",
      "voucherInputVariable": "depreciation-data",
      "aiPrompt": "Skapa avskrivningsverifikation. Använd lämpliga avskrivningskonton (7810-7890) för kostnader och minska motsvarande tillgångskonton.",
      "voucherText": "Avskrivningar ${current-month}",
      "variableName": "depreciation-voucher"
    },
    {
      "id": "step-8",
      "type": "eAccountingUploadAndCreateVoucher",
      "name": "Skapa periodiseringsverifikation",
      "description": "Ladda upp och skapa verifikation för periodiseringar",
      "fileInputVariable": "accruals-data",
      "filename": "periodiseringar-${current-month}.pdf",
      "contentType": "application/pdf",
      "fileComment": "Periodiseringar för ${current-month}",
      "voucherInputVariable": "accruals-data",
      "aiPrompt": "Skapa periodiseringsverifikation. Använd lämpliga periodiseringskonton (1410-1490 för fordringar, 2410-2490 för skulder) och motsvarande kostnadskonton.",
      "voucherText": "Periodiseringar ${current-month}",
      "variableName": "accruals-voucher"
    },
    {
      "id": "step-9",
      "type": "takeScreenshot",
      "name": "Dokumentera månadsavslut",
      "description": "Ta skärmdump som bekräftelse på genomfört månadsavslut",
      "variableName": "month-end-confirmation"
    }
  ]
}
```

## Användning av Exempel

1. **Kopiera JSON-koden** för det exempel du vill använda
2. **Anpassa URL:er och selektorer** till ditt specifika system
3. **Konfigurera credentials** för de system du ska integrera med
4. **Justera AI-prompter** för dina specifika bokföringsbehov
5. **Testa flödet** i utvecklingsmiljö först
6. **Övervaka loggar** för att säkerställa korrekt funktion

## Tips för Anpassning

- **Selektorer**: Använd utvecklarverktyg för att hitta rätt CSS-selektorer
- **Variabelnamn**: Använd beskrivande namn för enklare felsökning
- **AI-prompter**: Var specifik med kontonummer och bokföringsregler
- **Filnamn**: Använd dynamiska filnamn med variabler för bättre organisation
- **Felhantering**: Lägg till extra steg för validering vid behov
