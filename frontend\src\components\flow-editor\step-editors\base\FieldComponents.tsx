import { RpaStep } from '@rpa-project/shared'
import { ElementSelector } from '../../ElementSelector'
import { VariableInput } from '../../VariableInput'
import { StepEditorField, getInputStyle, getSelectStyle } from './BaseStepEditor'

interface FieldComponentProps {
  compact?: boolean
  steps?: RpaStep[]
  currentStepIndex?: number
}

interface SelectorFieldProps extends FieldComponentProps {
  label: string
  value: string
  onChange: (value: string) => void
  placeholder?: string
  fullWidth?: boolean
}

export function SelectorField({ 
  label, 
  value, 
  onChange, 
  placeholder, 
  compact = false, 
  fullWidth = false 
}: SelectorFieldProps) {
  return (
    <StepEditorField label={label} compact={compact} fullWidth={fullWidth}>
      <ElementSelector
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        style={getInputStyle(compact)}
      />
    </StepEditorField>
  )
}

interface VariableFieldProps extends FieldComponentProps {
  label: string
  value: string
  onChange: (value: string) => void
  placeholder?: string
  type?: 'text' | 'url' | 'number'
  fullWidth?: boolean
}

export function VariableField({ 
  label, 
  value, 
  onChange, 
  placeholder, 
  type, 
  compact = false, 
  fullWidth = false,
  steps = [],
  currentStepIndex = 0
}: VariableFieldProps) {
  return (
    <StepEditorField label={label} compact={compact} fullWidth={fullWidth}>
      <VariableInput
        type={type}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        steps={steps}
        currentStepIndex={currentStepIndex}
        style={getInputStyle(compact)}
      />
    </StepEditorField>
  )
}

interface TextFieldProps extends FieldComponentProps {
  label: string
  value: string | number
  onChange: (value: string) => void
  placeholder?: string
  type?: 'text' | 'number' | 'url'
  min?: string | number
  max?: string | number
  fullWidth?: boolean
}

export function TextField({ 
  label, 
  value, 
  onChange, 
  placeholder, 
  type = 'text', 
  min, 
  max, 
  compact = false, 
  fullWidth = false 
}: TextFieldProps) {
  return (
    <StepEditorField label={label} compact={compact} fullWidth={fullWidth}>
      <input
        type={type}
        className="form-input"
        style={getInputStyle(compact)}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        min={min}
        max={max}
      />
    </StepEditorField>
  )
}

interface TextAreaFieldProps extends FieldComponentProps {
  label: string
  value: string
  onChange: (value: string) => void
  placeholder?: string
  minHeight?: string
  fullWidth?: boolean
}

export function TextAreaField({ 
  label, 
  value, 
  onChange, 
  placeholder, 
  minHeight = '60px', 
  compact = false, 
  fullWidth = false 
}: TextAreaFieldProps) {
  return (
    <StepEditorField label={label} compact={compact} fullWidth={fullWidth}>
      <textarea
        className="form-input"
        style={{
          ...getInputStyle(compact),
          minHeight,
          resize: 'vertical'
        }}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
      />
    </StepEditorField>
  )
}

interface SelectFieldProps extends FieldComponentProps {
  label: string
  value: string
  onChange: (value: string) => void
  options: { value: string; label: string }[]
  fullWidth?: boolean
}

export function SelectField({ 
  label, 
  value, 
  onChange, 
  options, 
  compact = false, 
  fullWidth = false 
}: SelectFieldProps) {
  return (
    <StepEditorField label={label} compact={compact} fullWidth={fullWidth}>
      <select
        className="form-input"
        style={getSelectStyle(compact)}
        value={value}
        onChange={(e) => onChange(e.target.value)}
      >
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </StepEditorField>
  )
}

interface CheckboxFieldProps extends FieldComponentProps {
  label: string
  checked: boolean
  onChange: (checked: boolean) => void
  description?: string
}

export function CheckboxField({ 
  label, 
  checked, 
  onChange, 
  description, 
  compact = false 
}: CheckboxFieldProps) {
  return (
    <StepEditorField label="" compact={compact}>
      <label style={{
        display: 'flex',
        alignItems: 'center',
        gap: '0.5rem',
        fontSize: compact ? '0.75rem' : '0.875rem',
        color: '#374151'
      }}>
        <input
          type="checkbox"
          checked={checked}
          onChange={(e) => onChange(e.target.checked)}
        />
        {label}
        {description && (
          <span style={{ color: '#6b7280', fontSize: '0.75rem' }}>
            ({description})
          </span>
        )}
      </label>
    </StepEditorField>
  )
}

interface NumberFieldProps extends FieldComponentProps {
  label: string
  value: number
  onChange: (value: number) => void
  min?: number
  max?: number
  placeholder?: string
  fullWidth?: boolean
}

export function NumberField({ 
  label, 
  value, 
  onChange, 
  min, 
  max, 
  placeholder, 
  compact = false, 
  fullWidth = false 
}: NumberFieldProps) {
  return (
    <StepEditorField label={label} compact={compact} fullWidth={fullWidth}>
      <input
        type="number"
        className="form-input"
        style={getInputStyle(compact)}
        value={value}
        onChange={(e) => onChange(parseInt(e.target.value) || 0)}
        min={min}
        max={max}
        placeholder={placeholder}
      />
    </StepEditorField>
  )
}

// Common field sets for reuse
interface CommonFieldsProps extends FieldComponentProps {
  step: any
  updateStep: (updates: any) => void
}

export function CommonStepFields({ step, updateStep, compact = false }: CommonFieldsProps) {
  return (
    <>
      <TextAreaField
        label="Beskrivning (valfritt)"
        value={step.description || ''}
        onChange={(value) => updateStep({ description: value })}
        placeholder="Valfri beskrivning för detta steg"
        compact={compact}
        fullWidth={true}
      />
      <NumberField
        label="Timeout (ms)"
        value={step.timeout || 30000}
        onChange={(value) => updateStep({ timeout: value })}
        min={1000}
        max={300000}
        placeholder="30000"
        compact={compact}
      />
    </>
  )
}
