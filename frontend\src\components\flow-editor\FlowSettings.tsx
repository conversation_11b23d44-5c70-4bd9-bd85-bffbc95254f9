interface FlowSettingsProps {
  settings: Record<string, any>
  onChange: (settings: Record<string, any>) => void
  onClose?: () => void
  flow?: {
    name: string
    description?: string
  }
  onFlowChange?: (updates: { name?: string; description?: string }) => void
  onSave?: () => void
  saving?: boolean
}

export function FlowSettings({ settings, onChange, onClose, flow, onFlowChange, onSave, saving }: FlowSettingsProps) {
  const updateSetting = (key: string, value: any) => {
    onChange({
      ...settings,
      [key]: value
    })
  }

  return (
    <div style={{ padding: '1.5rem' }}>
      {/* Flow Information Section */}
      <div style={{ marginBottom: '2rem' }}>
        <h4 style={{
          fontSize: '1rem',
          fontWeight: '600',
          color: '#374151',
          marginBottom: '1rem',
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem'
        }}>
          📝 Flow Information
        </h4>

        <div style={{
          display: 'grid',
          gridTemplateColumns: '1fr',
          gap: '1rem'
        }}>
          <div className="form-group">
            <label className="form-label">Flow Customer</label>
            <input
              type="text"
              className="form-input"
              value={flow?.name || ''}
              onChange={(e) => onFlowChange?.({ name: e.target.value })}
              placeholder="Enter flow customer"
              style={{ fontSize: '1rem' }}
            />
          </div>

          <div className="form-group">
            <label className="form-label">Description</label>
            <input
              type="text"
              className="form-input"
              value={flow?.description || ''}
              onChange={(e) => onFlowChange?.({ description: e.target.value })}
              placeholder="Enter flow description (optional)"
              style={{ fontSize: '0.875rem' }}
            />
          </div>
        </div>
      </div>
      {/* Browser Configuration Section */}
      <div style={{ marginBottom: '2rem' }}>
        <h4 style={{
          fontSize: '1rem',
          fontWeight: '600',
          color: '#374151',
          marginBottom: '1rem',
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem'
        }}>
          🌐 Browser Configuration
        </h4>

        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '1rem'
        }}>
          <div className="form-group">
            <label className="form-label">Browser Type</label>
            <select
              className="form-input form-select"
              value={settings.browserType || 'chromium'}
              onChange={(e) => updateSetting('browserType', e.target.value)}
            >
              <option value="chromium">Chromium</option>
              <option value="firefox">Firefox</option>
              <option value="webkit">WebKit</option>
            </select>
          </div>

          <div className="form-group">
            <label className="form-label">Headless Mode</label>
            <select
              className="form-input form-select"
              value={settings.headless ? 'true' : 'false'}
              onChange={(e) => updateSetting('headless', e.target.value === 'true')}
            >
              <option value="true">Yes (Headless)</option>
              <option value="false">No (Show Browser)</option>
            </select>
          </div>

          <div className="form-group">
            <label className="form-label">User Agent</label>
            <input
              type="text"
              className="form-input"
              value={settings.userAgent || ''}
              onChange={(e) => updateSetting('userAgent', e.target.value)}
              placeholder="Custom user agent (optional)"
            />
          </div>

          <div className="form-group">
            <label className="form-label">Locale</label>
            <input
              type="text"
              className="form-input"
              value={settings.locale || 'sv-SE'}
              onChange={(e) => updateSetting('locale', e.target.value)}
              placeholder="sv-SE"
            />
          </div>
        </div>
      </div>

      {/* Viewport & Performance Section */}
      <div style={{ marginBottom: '2rem' }}>
        <h4 style={{
          fontSize: '1rem',
          fontWeight: '600',
          color: '#374151',
          marginBottom: '1rem',
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem'
        }}>
          📐 Viewport & Performance
        </h4>

        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '1rem'
        }}>
          <div className="form-group">
            <label className="form-label">Viewport Width</label>
            <input
              type="number"
              className="form-input"
              value={settings.viewportWidth || 1280}
              onChange={(e) => updateSetting('viewportWidth', parseInt(e.target.value) || 1280)}
              min="320"
              max="3840"
            />
          </div>

          <div className="form-group">
            <label className="form-label">Viewport Height</label>
            <input
              type="number"
              className="form-input"
              value={settings.viewportHeight || 720}
              onChange={(e) => updateSetting('viewportHeight', parseInt(e.target.value) || 720)}
              min="240"
              max="2160"
            />
          </div>

          <div className="form-group">
            <label className="form-label">Default Timeout (ms)</label>
            <input
              type="number"
              className="form-input"
              value={settings.defaultTimeout || 30000}
              onChange={(e) => updateSetting('defaultTimeout', parseInt(e.target.value) || 30000)}
              min="1000"
              max="300000"
            />
          </div>

          <div className="form-group">
            <label className="form-label">Slow Motion (ms)</label>
            <input
              type="number"
              className="form-input"
              value={settings.slowMo || 0}
              onChange={(e) => updateSetting('slowMo', parseInt(e.target.value) || 0)}
              min="0"
              max="5000"
            />
          </div>
        </div>
      </div>

      {/* Advanced Options Section */}
      <div style={{ marginBottom: '2rem' }}>
        <h4 style={{
          fontSize: '1rem',
          fontWeight: '600',
          color: '#374151',
          marginBottom: '1rem',
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem'
        }}>
          ⚙️ Advanced Options
        </h4>

        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '1rem'
        }}>
          <label style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            padding: '0.75rem',
            backgroundColor: '#f8fafc',
            borderRadius: '0.5rem',
            border: '1px solid #e2e8f0',
            cursor: 'pointer',
            transition: 'all 0.2s'
          }}>
            <input
              type="checkbox"
              checked={settings.ignoreHTTPSErrors || false}
              onChange={(e) => updateSetting('ignoreHTTPSErrors', e.target.checked)}
              style={{ marginRight: '0.25rem' }}
            />
            <span style={{ fontSize: '0.875rem', color: '#374151' }}>Ignore HTTPS Errors</span>
          </label>

          <label style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            padding: '0.75rem',
            backgroundColor: '#f8fafc',
            borderRadius: '0.5rem',
            border: '1px solid #e2e8f0',
            cursor: 'pointer',
            transition: 'all 0.2s'
          }}>
            <input
              type="checkbox"
              checked={settings.acceptDownloads || false}
              onChange={(e) => updateSetting('acceptDownloads', e.target.checked)}
              style={{ marginRight: '0.25rem' }}
            />
            <span style={{ fontSize: '0.875rem', color: '#374151' }}>Accept Downloads</span>
          </label>

          <label style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            padding: '0.75rem',
            backgroundColor: '#f8fafc',
            borderRadius: '0.5rem',
            border: '1px solid #e2e8f0',
            cursor: 'pointer',
            transition: 'all 0.2s'
          }}>
            <input
              type="checkbox"
              checked={settings.recordVideo || false}
              onChange={(e) => updateSetting('recordVideo', e.target.checked)}
              style={{ marginRight: '0.25rem' }}
            />
            <span style={{ fontSize: '0.875rem', color: '#374151' }}>Record Video</span>
          </label>

          <label style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            padding: '0.75rem',
            backgroundColor: '#f8fafc',
            borderRadius: '0.5rem',
            border: '1px solid #e2e8f0',
            cursor: 'pointer',
            transition: 'all 0.2s'
          }}>
            <input
              type="checkbox"
              checked={settings.recordTrace || false}
              onChange={(e) => updateSetting('recordTrace', e.target.checked)}
              style={{ marginRight: '0.25rem' }}
            />
            <span style={{ fontSize: '0.875rem', color: '#374151' }}>Record Trace</span>
          </label>
        </div>
      </div>

      {/* Environment Variables Section */}
      <div>
        <h4 style={{
          fontSize: '1rem',
          fontWeight: '600',
          color: '#374151',
          marginBottom: '1rem',
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem'
        }}>
          🔧 Environment Variables
        </h4>

        <div className="form-group">
          <label className="form-label">Environment Variables (JSON)</label>
          <textarea
            className="form-input form-textarea"
            value={settings.environmentVariables ? JSON.stringify(settings.environmentVariables, null, 2) : '{}'}
            onChange={(e) => {
              try {
                const parsed = JSON.parse(e.target.value)
                updateSetting('environmentVariables', parsed)
              } catch {
                // Invalid JSON, don't update
              }
            }}
            placeholder='{"VAR_NAME": "value"}'
            rows={4}
            style={{ fontFamily: 'Monaco, Consolas, "Courier New", monospace' }}
          />
          <p style={{
            fontSize: '0.75rem',
            color: '#6b7280',
            marginTop: '0.5rem',
            fontStyle: 'italic'
          }}>
            JSON object with environment variables for the flow execution
          </p>
        </div>
      </div>

      {/* Save Button */}
      <div style={{
        borderTop: '1px solid #e5e7eb',
        padding: '1.5rem',
        margin: '0 -1.5rem -1.5rem -1.5rem',
        backgroundColor: '#f8fafc',
        display: 'flex',
        justifyContent: 'flex-end',
        gap: '0.75rem'
      }}>
        <button
          onClick={onClose}
          className="btn btn-outline"
          style={{ padding: '0.75rem 1.5rem' }}
        >
          Cancel
        </button>
        <button
          onClick={onSave}
          disabled={saving}
          className="btn btn-primary"
          style={{
            padding: '0.75rem 1.5rem',
            opacity: saving ? 0.5 : 1,
            cursor: saving ? 'not-allowed' : 'pointer'
          }}
        >
          {saving ? '💾 Saving...' : '💾 Save Settings'}
        </button>
      </div>
    </div>
  )
}
