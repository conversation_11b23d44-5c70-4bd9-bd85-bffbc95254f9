import { RpaStepBase } from './base';

// Extraction steps
export interface ExtractTextStep extends RpaStepBase {
  type: 'extractText';
  selector: string;
  variableName?: string; // Variable name to store extracted text (optional, defaults to 'extractedText')
}

export interface ExtractAttributeStep extends RpaStepBase {
  type: 'extractAttribute';
  selector: string;
  attribute: string;
  variableName?: string; // Variable name to store extracted attribute (optional, defaults to 'extractedAttribute')
}

export interface TakeScreenshotStep extends RpaStepBase {
  type: 'takeScreenshot';
  path?: string;
  fullPage?: boolean;
  variableName?: string; // Variable name to store base64 encoded image
}
