# Felsökning

Denna guide hj<PERSON><PERSON>per dig att lösa vanliga problem i RPA-systemet.

## Allmän <PERSON>ning

### 1. Kryptering och Master Key Problem

#### Problem: Fortnox tokens kan inte dekrypteras mellan `npm run dev` och `docker compose up`

**Symptom:**
- To<PERSON>s fungerar när du kör `docker compose up`
- <PERSON><PERSON> tokens misslyckas när du kör `npm run dev`
- Felmeddelande om dekryptering

**Orsak:**
Olika `process.cwd()` sökvägar resulterar i olika master.key filer:
- `npm run dev`: Använder `data/master.key`
- `docker compose up`: Använder `backend/data/master.key`

**Lösning:**
1. Kontrollera att `MASTER_KEY_PATH` är korrekt satt i miljöfiler:
   ```bash
   # I backend/.env
   MASTER_KEY_PATH=../data/master.key

   # I .env.docker
   MASTER_KEY_PATH=/app/data/master.key
   ```

2. Synkronisera master.key filer:
   ```bash
   # Kopiera den nyare filen
   Copy-Item -Path "data\master.key" -Destination "backend\data\master.key" -Force
   ```

3. Verifiera att båda filerna är identiska:
   ```bash
   Get-FileHash data\master.key, backend\data\master.key
   ```

### 2. Kontrollera Loggar

```bash
# Backend loggar
tail -f backend/logs/app.log

# Frontend console
# Öppna Developer Tools i webbläsaren

# Redis loggar
redis-cli monitor
```

### 2. Verifiera Miljövariabler

```bash
# Kontrollera att alla nödvändiga miljövariabler är satta
echo $OPENAI_API_KEY
echo $REDIS_URL
echo $DATABASE_URL
```

### 3. Testa Anslutningar

```bash
# Testa Redis-anslutning
redis-cli ping

# Testa databas-anslutning
npm run db:test

# Testa API-endpoints
curl http://localhost:3001/api/health
```

## TypeScript Kompileringsfel

### Problem: "Cannot find module"

```
Error: Cannot find module '../shared/src/types'
```

**Lösning:**

1. Kontrollera att shared package är byggt:
```bash
cd shared && npm run build
```

2. Verifiera import-sökvägar:
```typescript
// Korrekt
import { RpaStep } from '../../../shared/src/types';

// Felaktig
import { RpaStep } from '../shared/types';
```

3. Kontrollera tsconfig.json paths:
```json
{
  "compilerOptions": {
    "paths": {
      "@shared/*": ["../shared/src/*"]
    }
  }
}
```

### Problem: "Type errors in shared package"

```
Error: Property 'newField' does not exist on type 'RpaStep'
```

**Lösning:**

1. Uppdatera union type i shared/src/types/steps/index.ts
2. Bygg om shared package
3. Starta om TypeScript server i IDE

### Problem: "Circular dependency"

```
Error: Circular dependency detected
```

**Lösning:**

1. Identifiera cykeln med dependency-cruiser:
```bash
npx dependency-cruiser --output-type dot src | dot -T svg > deps.svg
```

2. Bryt cykeln genom att:
   - Flytta gemensam kod till separat fil
   - Använd dependency injection
   - Omstrukturera imports

## Runtime-fel

### Problem: "Runner not found"

```
Error: No runner found for step type 'myNewStep'
```

**Lösning:**

1. Kontrollera step-runner mappning:
```typescript
// backend/src/runners/registry/stepTypes.ts
export const STEP_RUNNER_MAPPING = {
  myNewStep: 'playwright', // Lägg till denna rad
};
```

2. Verifiera att runner kan hantera steget:
```typescript
// PlaywrightRunner.ts
canExecuteStep(step: RpaStep): boolean {
  const supportedSteps = [
    'myNewStep', // Lägg till här
  ];
  return supportedSteps.includes(step.type);
}
```

### Problem: "Page not available"

```
Error: Browser page är inte tillgänglig
```

**Lösning:**

1. Kontrollera att PlaywrightRunner startats:
```typescript
await runner.start(); // Måste anropas före executeStep
```

2. Verifiera browser-initialisering:
```typescript
// PlaywrightRunner.ts
async start(): Promise<void> {
  this.browser = await chromium.launch();
  this.page = await this.browser.newPage();
}
```

3. Kontrollera att browser inte stängts:
```typescript
if (!this.page || this.page.isClosed()) {
  throw new Error('Browser page är inte tillgänglig');
}
```

### Problem: "OpenAI API error"

```
Error: OpenAI API request failed with status 401
```

**Lösning:**

1. Kontrollera API-nyckel:
```bash
echo $OPENAI_API_KEY
```

2. Testa API-nyckel:
```bash
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
  https://api.openai.com/v1/models
```

3. Kontrollera rate limits och billing

## Frontend-problem

### Problem: "Step editor not rendering"

**Lösning:**

1. Kontrollera att editor är registrerad:
```typescript
// StepEditorRegistry.tsx
case 'myNewStep':
  return <MyNewStepEditor {...props} step={step as MyNewStep} />;
```

2. Verifiera TypeScript-typer:
```typescript
interface MyNewStepEditorProps extends BaseStepEditorProps {
  step: MyNewStep; // Korrekt typ
}
```

3. Kontrollera console för React-fel

### Problem: "Step not appearing in toolbar"

**Lösning:**

1. Kontrollera step-definition:
```typescript
// step-definitions/index.ts
export const stepDefinitions: StepDefinition[] = [
  myNewStepDefinition, // Lägg till här
];
```

2. Verifiera import:
```typescript
import { myNewStepDefinition } from './interaction';
```

3. Kontrollera att definition är giltig:
```typescript
const definition = {
  type: 'myNewStep',
  name: 'Mitt Steg',
  category: 'interaction',
  icon: 'MousePointer',
  // ... andra obligatoriska fält
};
```

### Problem: "Variables not updating"

**Lösning:**

1. Kontrollera att variabler skapas i runner:
```typescript
// I step-execution
context.variables['var-my-step-result'] = result;
```

2. Verifiera variable-naming:
```typescript
// Korrekt format
'var-step-type-property'

// Felaktigt
'myVariable'
```

3. Kontrollera React state updates:
```typescript
// Använd functional updates
setVariables(prev => ({
  ...prev,
  [variableName]: value
}));
```

### Problem: "Variabelväljaren visar inte mina variabler"

**Symptom:** Steg som skapar variabler visas inte i variabelväljaren, särskilt när default-variabelnamn används.

**Orsak:** Frontend-komponenter kollade bara efter explicita `variableName` fält, inte default-namn.

**Lösning (Fixad i v1.2.0):**

Frontend använder nu samma logik som backend för variabeldetektering:

```typescript
// Före fix - missade default-variabler
if (extractStep.variableName) {
  variables.push(extractStep.variableName);
}

// Efter fix - inkluderar default-variabler
const variableName = extractStep.variableName || getDefaultVariableName(step.type, i);
variables.push(variableName);
```

**Verifiering:**

1. Skapa ett extractText-steg utan att ange variabelnamn
2. Lägg till ett annat steg som använder variabler
3. Variabelväljaren ska visa `extractedText_1` som tillgänglig variabel

**Påverkade komponenter:**
- `VariableHelper.tsx` - Variabelväljaren
- `VariablesModal.tsx` - Variabelvisning

## Performance-problem

### Problem: "Slow step execution"

**Lösning:**

1. Kontrollera timeouts:
```typescript
// Öka timeout för långsamma element
await page.waitForSelector(selector, { timeout: 30000 });
```

2. Optimera selectors:
```typescript
// Snabbare
'#specific-id'

// Långsammare
'div > span.class > a[href*="text"]'
```

3. Använd effektiva väntan:
```typescript
// Bra
await page.waitForLoadState('networkidle');

// Dåligt
await page.waitForTimeout(5000);
```

### Problem: "Memory leaks"

**Lösning:**

1. Stäng browser-resurser:
```typescript
async stop(): Promise<void> {
  if (this.page) {
    await this.page.close();
  }
  if (this.browser) {
    await this.browser.close();
  }
}
```

2. Rensa event listeners:
```typescript
useEffect(() => {
  const handler = () => {};
  window.addEventListener('resize', handler);
  
  return () => {
    window.removeEventListener('resize', handler);
  };
}, []);
```

3. Avbryt pågående requests:
```typescript
useEffect(() => {
  const controller = new AbortController();
  
  fetch('/api/data', { signal: controller.signal });
  
  return () => controller.abort();
}, []);
```

## Databas-problem

### Problem: "Connection timeout"

**Lösning:**

1. Kontrollera anslutningssträng:
```bash
echo $DATABASE_URL
```

2. Testa anslutning:
```bash
npm run db:ping
```

3. Kontrollera connection pool:
```typescript
// Öka pool-storlek
const pool = new Pool({
  max: 20,
  idleTimeoutMillis: 30000,
});
```

### Problem: "Migration errors"

**Lösning:**

1. Kontrollera migration-status:
```bash
npm run db:migrate:status
```

2. Återställ till tidigare version:
```bash
npm run db:migrate:down
```

3. Kör migrations igen:
```bash
npm run db:migrate:up
```

## Redis-problem

### Problem: "Redis connection failed"

**Lösning:**

1. Kontrollera Redis-server:
```bash
redis-cli ping
```

2. Verifiera anslutningssträng:
```bash
echo $REDIS_URL
```

3. Kontrollera Redis-konfiguration:
```typescript
const redis = new Redis({
  host: 'localhost',
  port: 6379,
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
});
```

### Problem: "Queue jobs not processing"

**Lösning:**

1. Kontrollera queue-worker:
```bash
npm run queue:worker
```

2. Inspektera queue-status:
```bash
npm run queue:status
```

3. Rensa stuck jobs:
```bash
npm run queue:clean
```

### Problem: "Retry fungerar inte för failed executions"

**Symptom:** BullMQ retry-policy är konfigurerad men failed executions körs inte igen vid retry.

**Orsak (Fixad i v1.2.2):** Worker-koden kontrollerade execution-status och hoppade över retry för failed executions.

**Lösning (Implementerad):**

Worker-logiken har uppdaterats för att tillåta retry av failed executions:

```typescript
// Före fix - blockerade retry
if (execution.status === 'completed' || execution.status === 'failed') {
  return { status: execution.status, message: `Execution already ${execution.status}` };
}

// Efter fix - tillåter retry av failed executions
if (execution.status === 'completed') {
  return { status: execution.status, message: 'Execution already completed' };
}

if (execution.status === 'failed') {
  console.log(`🔄 Retrying failed execution: ${executionId}`);
  // Fortsätt med ny körning...
}
```

**Verifiering:**

1. Skapa ett flöde som failar (t.ex. med AI-balansfel)
2. Vänta på automatisk retry eller trigga manuell retry
3. Kontrollera att execution körs igen med nya AI-svar
4. Loggar ska visa "Retrying failed execution" meddelanden

**Retry-beteende efter fix:**

- ✅ **Failed executions** - Körs igen med samma input men potentiellt olika AI-svar
- ✅ **Cancelled executions** - Hoppas över (ingen retry)
- ✅ **Completed executions** - Hoppas över (ingen retry)
- ✅ **Running executions** - Fortsätter normalt

## Debugging-verktyg

### 1. Playwright Inspector

```typescript
// Aktivera debug-läge
await page.pause(); // Pausar execution för manuell inspektion
```

```bash
# Kör med headed browser
PWDEBUG=1 npm run test
```

### 2. React Developer Tools

1. Installera browser-extension
2. Inspektera component state
3. Profila performance

### 3. Node.js Debugger

```bash
# Starta med debugger
node --inspect-brk backend/src/index.js

# Anslut med Chrome DevTools
# Gå till chrome://inspect
```

### 4. Logging

```typescript
// Strukturerad logging
this.logInfo('Step execution', {
  stepType: step.type,
  stepId: step.id,
  duration: Date.now() - startTime
});

// Debug-logging
if (process.env.DEBUG) {
  console.log('Debug info:', debugData);
}
```

## Vanliga Fel och Lösningar

| Fel | Orsak | Lösning |
|-----|-------|---------|
| `ECONNREFUSED` | Service inte igång | Starta service |
| `EADDRINUSE` | Port redan använd | Ändra port eller döda process |
| `MODULE_NOT_FOUND` | Saknad dependency | `npm install` |
| `TIMEOUT` | För kort timeout | Öka timeout-värde |
| `SELECTOR_NOT_FOUND` | Element finns inte | Kontrollera selector |
| `PERMISSION_DENIED` | Saknar behörighet | Kontrollera file permissions |

### Problem: "Download event timeout"

```
Error: page.waitForEvent: Timeout 30000ms exceeded while waiting for event "download"
```

**Orsak:** Webbsidan utlöser inte download-eventet som förväntat, eller länken öppnar filen direkt i webbläsaren istället för att ladda ner den.

**Lösning (Fixad i v1.2.1):**

1. **Aktivera Force Download:** Markera "Forcera nedladdning" i downloadFile-steget för att lägga till `download`-attribut på länkar.

2. **Automatisk Fallback:** Systemet försöker automatiskt alternativ metod om download-eventet misslyckas:
   - Hämtar href-attributet från elementet
   - Navigerar direkt till URL:en
   - Laddar ner innehållet via HTTP-response

3. **Förbättrad Felhantering:** Systemet loggar varningar och försöker alternativa metoder innan det misslyckas.

**Verifiering:**
```typescript
// downloadFile-steget stöder nu:
{
  type: 'downloadFile',
  triggerSelector: 'a[href*="download"]',
  forceDownload: true,  // Lägg till download-attribut
  saveToFile: false,    // Spara endast i minnet
  timeout: 30000        // Öka timeout vid behov
}
```

**Påverkade filer:**
- `backend/src/runners/playwright/stepExecutors/extraction.ts` - Förbättrad download-logik
- `shared/src/types/steps/files.ts` - Uppdaterad type-definition

## Få Hjälp

### 1. Kontrollera Dokumentation

- [Arkitektur](./architecture.md)
- [Lägga till Steg](./adding-new-steps.md)
- [Lägga till Runners](./adding-new-runners.md)

### 2. Sök i Issues

Sök i GitHub issues för liknande problem.

### 3. Skapa Issue

Om du inte hittar lösning, skapa en ny issue med:

- Detaljerad beskrivning av problemet
- Steg för att reproducera
- Felmeddelanden och stack traces
- Miljöinformation (OS, Node version, etc.)

### 4. Debug Information

Inkludera alltid:

```bash
# System info
node --version
npm --version
git --version

# Package versions
npm list --depth=0

# Environment
env | grep -E "(NODE|NPM|PATH)"
```
