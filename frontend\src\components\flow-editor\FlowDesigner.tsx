import { useCallback, useState } from 'react'
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core'
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable'
import {
  useSortable,
} from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { RpaFlow, createStepFromType, RpaStep } from '@rpa-project/shared'
import { StepToolbar } from './StepToolbar'
import { AIAssistantPanel } from './AIAssistantPanel'
import { StepEditor } from './StepEditor'
import { useScrollFade } from '../../hooks/useScrollFade'

interface FlowDesignerProps {
  flow: RpaFlow
  onChange: (flow: RpaFlow) => void
  showAIPanel?: boolean
}

// SortableItem component for individual steps
function SortableItem({
  step,
  index,
  isSelected,
  isEditing,
  onSelect,
  onEdit,
  onUpdate,
  onDelete,
  getStepIcon,
  allSteps
}: {
  step: RpaStep
  index: number
  isSelected: boolean
  isEditing: boolean
  onSelect: (id: string) => void
  onEdit: (id: string | null) => void
  onUpdate: (id: string, step: RpaStep) => void
  onDelete: (id: string) => void
  getStepIcon: (type: string) => { icon: string; color: string }
  allSteps: RpaStep[]
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: step.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  }

  const { icon, color } = getStepIcon(step.type)

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
    >
      <div
        style={{
          backgroundColor: isSelected ? '#fef7f7' : 'white',
          border: `1px solid ${isSelected ? '#fd746c' : '#e5e7eb'}`,
          borderRadius: '1rem',
          padding: '1rem',
          cursor: 'pointer',
          transition: 'all 0.3s ease-in-out',
          overflow: 'hidden',
          transform: isDragging ? 'rotate(2deg)' : 'none',
        }}
        onClick={() => onSelect(step.id)}
      >
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          {/* Step Info */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', flex: 1 }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: '2rem',
              height: '2rem',
              backgroundColor: color + '20',
              borderRadius: '0.375rem',
              fontSize: '1rem'
            }}>
              {icon}
            </div>

            <div style={{ flex: 1 }}>
              <div style={{
                fontWeight: '600',
                color: '#1a0f0f',
                fontSize: '0.875rem',
                marginBottom: '0.25rem'
              }}>
                {step.type}
              </div>

              {/* Step details */}
              <div className="secondary-text" style={{ marginTop: '0.25rem' }}>
                {step.type === 'navigate' && step.url && (
                  <span>Navigate to: {step.url}</span>
                )}
                {(step.type === 'click' || step.type === 'fill' || step.type === 'type') && step.selector && (
                  <span>Selector: {step.selector}</span>
                )}
                {step.type === 'fill' && step.value && (
                  <span> • Value: {step.value}</span>
                )}
                {step.type === 'waitForTimeout' && step.timeout && (
                  <span>Wait: {step.timeout}ms</span>
                )}
                {step.type === 'waitForSelector' && step.selector && (
                  <span>Wait for: {step.selector}</span>
                )}
                {!['navigate', 'click', 'fill', 'type', 'waitForTimeout', 'waitForSelector'].includes(step.type) && (
                  <span>Step {index + 1}</span>
                )}
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
            {/* Drag Handle */}
            <div
              {...listeners}
              className="action-button-small icon-only"
              title="Drag to reorder"
              style={{ cursor: 'grab' }}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="9" cy="12" r="1"></circle>
                <circle cx="9" cy="5" r="1"></circle>
                <circle cx="9" cy="19" r="1"></circle>
                <circle cx="15" cy="12" r="1"></circle>
                <circle cx="15" cy="5" r="1"></circle>
                <circle cx="15" cy="19" r="1"></circle>
              </svg>
            </div>

            <button
              onClick={(e) => {
                e.stopPropagation()
                onEdit(isEditing ? null : step.id)
              }}
              className="action-button-small icon-only"
              title="Edit Step"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
              </svg>
            </button>

            <button
              onClick={(e) => {
                e.stopPropagation()
                if (confirm('Are you sure you want to delete this step?')) {
                  onDelete(step.id)
                }
              }}
              className="action-button-small danger"
              title="Delete Step"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <polyline points="3,6 5,6 21,6"></polyline>
                <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
              </svg>
            </button>
          </div>
        </div>

        {/* Step Editor */}
        {isEditing && (
          <div style={{
            marginTop: '1rem',
            paddingTop: '1rem',
            borderTop: '1px solid #e5e7eb',
            backgroundColor: '#f8fafc',
            borderRadius: '1rem',
            padding: '1.5rem',
            marginLeft: '-1rem',
            marginRight: '-1rem',
            marginBottom: '-1rem',
            animation: 'expandStep 0.3s ease-in-out'
          }}>
            <StepEditor
              step={step}
              onSave={(updatedStep) => {
                onUpdate(step.id, updatedStep)
                onEdit(null)
              }}
              onCancel={() => onEdit(null)}
              compact={true}
              steps={allSteps}
              currentStepIndex={index}
            />
          </div>
        )}
      </div>
    </div>
  )
}

export function FlowDesigner({ flow, onChange, showAIPanel = true }: FlowDesignerProps) {
  const [selectedStepId, setSelectedStepId] = useState<string | null>(null)
  const [editingStepId, setEditingStepId] = useState<string | null>(null)
  const { containerRef, overlayRef, fadeOverlayStyle } = useScrollFade()

  // Get the selected step
  const selectedStep = selectedStepId ? flow.steps.find(step => step.id === selectedStepId) : null

  // Function to update a specific step
  const updateStep = useCallback((stepId: string, updatedStep: RpaStep) => {
    const updatedSteps = flow.steps.map(step =>
      step.id === stepId ? updatedStep : step
    )

    onChange({
      ...flow,
      steps: updatedSteps,
      updatedAt: new Date()
    })
  }, [flow, onChange])

  // Function to add a new step
  const addStep = useCallback((stepType: string) => {
    try {
      const newStep = createStepFromType(stepType)

      onChange({
        ...flow,
        steps: [...flow.steps, newStep],
        updatedAt: new Date()
      })
    } catch (error) {
      console.error('Error adding step:', error)
      alert(`Error adding ${stepType} step: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }, [flow, onChange])

  // Add multiple steps from AI
  const addSteps = useCallback((steps: RpaStep[]) => {
    try {
      onChange({
        ...flow,
        steps: [...flow.steps, ...steps],
        updatedAt: new Date()
      })
    } catch (error) {
      console.error('Error adding steps:', error)
      alert(`Error adding steps: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }, [flow, onChange])

  // Function to delete a step
  const deleteStep = useCallback((stepId: string) => {
    const updatedSteps = flow.steps.filter(step => step.id !== stepId)

    onChange({
      ...flow,
      steps: updatedSteps,
      updatedAt: new Date()
    })

    // Clear selection if deleted step was selected
    if (selectedStepId === stepId) {
      setSelectedStepId(null)
    }
    if (editingStepId === stepId) {
      setEditingStepId(null)
    }
  }, [flow, onChange, selectedStepId, editingStepId])



  // Set up sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  )

  // Function to handle drag and drop
  const handleDragEnd = useCallback((event: DragEndEvent) => {
    console.log('handleDragEnd called:', event)

    const { active, over } = event

    if (!over) {
      console.log('No over target, drag cancelled')
      return
    }

    if (active.id !== over.id) {
      const oldIndex = flow.steps.findIndex(step => step.id === active.id)
      const newIndex = flow.steps.findIndex(step => step.id === over.id)

      console.log('Moving from index', oldIndex, 'to', newIndex)

      const updatedSteps = arrayMove(flow.steps, oldIndex, newIndex)

      onChange({
        ...flow,
        steps: updatedSteps,
        updatedAt: new Date()
      })
    }
  }, [flow, onChange])

  // Get step type icon and color
  const getStepIcon = (stepType: string) => {
    switch (stepType) {
      case 'navigate':
        return { icon: '🌐', color: '#3b82f6' }
      case 'click':
        return { icon: '👆', color: '#10b981' }
      case 'fill':
      case 'type':
        return { icon: '✏️', color: '#10b981' }
      case 'waitForSelector':
        return { icon: '⏳', color: '#f59e0b' }
      case 'waitForTimeout':
        return { icon: '⏱️', color: '#f59e0b' }
      case 'extractText':
        return { icon: '📄', color: '#8b5cf6' }
      case 'takeScreenshot':
        return { icon: '📸', color: '#8b5cf6' }
      case 'downloadFile':
        return { icon: '📥', color: '#8b5cf6' }
      case 'fillPassword':
        return { icon: '🔒', color: '#dc2626' }
      case 'fill2FA':
        return { icon: '🔐', color: '#dc2626' }
      case 'check':
      case 'uncheck':
        return { icon: '☑️', color: '#10b981' }
      case 'conditionalClick':
        return { icon: '🔀', color: '#10b981' }
      case 'goBack':
        return { icon: '⬅️', color: '#3b82f6' }
      case 'goForward':
        return { icon: '➡️', color: '#3b82f6' }
      case 'reload':
        return { icon: '🔄', color: '#3b82f6' }
      default:
        return { icon: '⚙️', color: '#6b7280' }
    }
  }

  return (
    <div style={{ height: '100%', display: 'flex' }}>
      {/* AI Assistant Panel - 500px Wide */}
      {showAIPanel && (
        <div style={{
          width: '40%',
          backgroundColor: '#f8fafc',
          display: 'flex',
          flexDirection: 'column'
        }}>
          <AIAssistantPanel
            flow={flow}
            onFlowChange={onChange}
            onAddSteps={addSteps}
            selectedStep={selectedStep}
            onUpdateStep={updateStep}
            onClearSelection={() => setSelectedStepId(null)}
          />
        </div>
      )}

      {/* Step Toolbar - 195px Wide */}
      <div style={{
        width: '220px',
        backgroundColor: 'transparent',
        display: 'flex',
        flexDirection: 'column'
      }}>
        <StepToolbar onAddStep={addStep} />
      </div>

      {/* Steps List */}
      <div
        className="custom-scrollbar"
        style={{
          flex: 1,
          height: '100%',
          overflow: 'auto',
          backgroundColor: '#fbf9f8',
          display: 'flex',
          flexDirection: 'column',
          position: 'relative'
        }}>
        <div
          ref={containerRef}
          className="custom-scrollbar"
          style={{
            flex: 1,
            overflow: 'auto',
            padding: '3rem 3rem 2rem 3rem'
          }}>
          {flow.steps.length === 0 ? (
            <div className="empty-state">
              <div className="empty-state-icon">🤖</div>
              <h3 className="empty-state-title">Inga steg än</h3>
              <p className="empty-state-subtitle">Lägg till steg från verktygspanelen eller använd AI-assistenten</p>
            </div>
          ) : (
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={flow.steps.map(step => step.id)}
                strategy={verticalListSortingStrategy}
              >
                <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                  {flow.steps.map((step, index) => (
                    <SortableItem
                      key={step.id}
                      step={step}
                      index={index}
                      isSelected={selectedStepId === step.id}
                      isEditing={editingStepId === step.id}
                      onSelect={setSelectedStepId}
                      onEdit={setEditingStepId}
                      onUpdate={updateStep}
                      onDelete={deleteStep}
                      getStepIcon={getStepIcon}
                      allSteps={flow.steps}
                    />
                  ))}
                </div>
              </SortableContext>
            </DndContext>
          )}
        </div>
        <div ref={overlayRef} style={fadeOverlayStyle} />
      </div>
    </div>
  )
}
