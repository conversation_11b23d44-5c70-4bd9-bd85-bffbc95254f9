@echo off
echo 🚀 Starting RPA Development Environment...

REM Check if Redis is running
redis-cli ping >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 Starting Redis...
    docker run -d --name rpa-redis -p 6379:6379 redis:alpine
    timeout /t 2 >nul
)

REM Install dependencies if needed
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    npm run setup
)

REM Start development servers
echo 🔧 Starting development servers...
npm run dev
