import { useState, useEffect } from 'react'
import { OAuth2<PERSON>rovider, Customer } from '@rpa-project/shared'
import { oauth2Api, customerApi } from '../../services/api'
import { Portal } from '../ui/Portal'

interface OAuth2ModalProps {
  customerId: string
  onSuccess: (provider: OAuth2Provider, tokenName: string) => void
  onCancel: () => void
}

interface OAuth2ProviderInfo {
  name: string
  displayName: string
  configured: boolean
  description: string
}

export function OAuth2Modal({ customerId, onSuccess, onCancel }: OAuth2ModalProps) {
  const [providers, setProviders] = useState<OAuth2ProviderInfo[]>([])
  const [customer, setCustomer] = useState<Customer | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedProvider, setSelectedProvider] = useState<OAuth2Provider | null>(null)
  const [tokenName, setTokenName] = useState('')
  const [description, setDescription] = useState('')
  const [initiating, setInitiating] = useState(false)

  useEffect(() => {
    loadProviders()
    loadCustomer()
  }, [])

  const loadProviders = async () => {
    try {
      setLoading(true)
      setError(null)
      console.log('Loading OAuth2 providers...')
      const response = await oauth2Api.getProviders()
      console.log('OAuth2 providers response:', response)
      if (response.success && response.data) {
        console.log('Setting providers:', response.data)
        setProviders(response.data)
      } else {
        console.error('Failed to load providers:', response.error)
        setError(response.error || 'Misslyckades att ladda OAuth2-providers')
      }
    } catch (err) {
      console.error('Error loading OAuth2 providers:', err)
      setError('Misslyckades att ladda OAuth2-providers')
    } finally {
      setLoading(false)
    }
  }

  const loadCustomer = async () => {
    try {
      const response = await customerApi.getCustomer(customerId)
      if (response.success && response.data) {
        setCustomer(response.data)
      } else {
        console.error('Failed to load customer:', response.error)
      }
    } catch (err) {
      console.error('Error loading customer:', err)
    }
  }

  const handleProviderSelect = (provider: OAuth2Provider) => {
    setSelectedProvider(provider)
    const customerNumber = customer?.customerNumber || 'OKÄND'
    setTokenName(`${customerNumber} - ${provider}`)
    setDescription(`OAuth2 token för ${provider}`)
  }

  const handleInitiateFlow = async () => {
    if (!selectedProvider || !tokenName.trim()) {
      setError('Välj en provider och ange ett tokennamn')
      return
    }

    try {
      setInitiating(true)
      setError(null)

      const response = await oauth2Api.initiateOAuth2Flow({
        customerId,
        provider: selectedProvider,
        tokenName: tokenName.trim(),
        description: description.trim() || undefined
      })

      if (response.success && response.data) {
        // Open OAuth2 authorization URL in a new window
        const authWindow = window.open(
          response.data.authUrl,
          'oauth2_auth',
          'width=600,height=700,scrollbars=yes,resizable=yes'
        )

        if (!authWindow) {
          setError('Kunde inte öppna auktoriseringsfönster. Kontrollera att popup-blockerare är inaktiverade.')
          return
        }

        // Poll for window closure (indicates completion)
        const pollTimer = setInterval(() => {
          if (authWindow.closed) {
            clearInterval(pollTimer)
            // Call success callback - the parent component should refresh the token list
            onSuccess(selectedProvider, tokenName.trim())
          }
        }, 1000)

        // Clean up timer after 10 minutes
        setTimeout(() => {
          clearInterval(pollTimer)
          if (!authWindow.closed) {
            authWindow.close()
          }
        }, 10 * 60 * 1000)

      } else {
        setError(response.error || 'Misslyckades att initiera OAuth2-flöde')
      }
    } catch (err) {
      setError('Misslyckades att initiera OAuth2-flöde')
      console.error('Error initiating OAuth2 flow:', err)
    } finally {
      setInitiating(false)
    }
  }

  return (
    <Portal>
      <div className="modal-overlay" onClick={onCancel}>
        <div
          className="modal-content"
          style={{ width: '100%', maxWidth: '70vw' }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Modal Header */}
          <div style={{
            flexShrink: 0,
            borderBottom: '1px solid #e5e7eb',
            margin: 0,
            padding: '1.5rem',
            textAlign: 'left'
          }}>
            <p className="dashboard-title" style={{ fontSize: '1.5rem', margin: 0 }}>
              OAuth2 Integration
            </p>
          </div>

          <div style={{ flex: 1, overflow: 'auto', padding: '1.5rem' }}>
            {error && (
              <div className="error-card" style={{ marginBottom: '1rem' }}>
                <h3 className="error-title">Fel</h3>
                <p className="error-message">{error}</p>
              </div>
            )}

            {loading ? (
              <div style={{ textAlign: 'center', padding: '2rem' }}>
                <p>Laddar OAuth2-providers...</p>
              </div>
            ) : (
              <>
                {/* Provider Selection */}
                <div className="form-group">
                  <label className="form-label">
                    Välj OAuth2 Provider *
                  </label>
                  <div style={{ display: 'grid', gap: '1rem', marginTop: '0.5rem' }}>
                    {providers.map((provider) => (
                      <div
                        key={provider.name}
                        className={`provider-card ${selectedProvider === provider.name ? 'selected' : ''} ${!provider.configured ? 'disabled' : ''}`}
                        onClick={() => provider.configured && handleProviderSelect(provider.name as OAuth2Provider)}
                        style={{
                          border: '2px solid #e5e7eb',
                          borderRadius: '0.5rem',
                          padding: '1rem',
                          cursor: provider.configured ? 'pointer' : 'not-allowed',
                          backgroundColor: selectedProvider === provider.name ? '#f0f9ff' : '#ffffff',
                          borderColor: selectedProvider === provider.name ? '#0ea5e9' : '#e5e7eb',
                          opacity: provider.configured ? 1 : 0.5
                        }}
                      >
                        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
                          <span style={{ fontSize: '1.25rem', fontWeight: '600' }}>
                            {provider.displayName}
                          </span>
                          {!provider.configured && (
                            <span style={{ 
                              fontSize: '0.75rem', 
                              color: '#dc2626', 
                              backgroundColor: '#fef2f2', 
                              padding: '0.25rem 0.5rem', 
                              borderRadius: '0.25rem' 
                            }}>
                              Ej konfigurerad
                            </span>
                          )}
                        </div>
                        <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: 0 }}>
                          {provider.description}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Token Configuration */}
                {selectedProvider && (
                  <>
                    <div className="form-group">
                      <label className="form-label">
                        Tokennamn *
                      </label>
                      <input
                        type="text"
                        className="form-input"
                        value={tokenName}
                        onChange={(e) => setTokenName(e.target.value)}
                        placeholder="Ange tokennamn"
                        required
                        maxLength={100}
                      />
                      <div style={{ fontSize: '0.75rem', color: '#6b7280', marginTop: '0.25rem' }}>
                        Unikt namn för denna token (max 100 tecken)
                      </div>
                    </div>

                    <div className="form-group">
                      <label className="form-label">
                        Beskrivning
                      </label>
                      <textarea
                        className="form-input"
                        value={description}
                        onChange={(e) => setDescription(e.target.value)}
                        placeholder="Valfri beskrivning av token"
                        maxLength={500}
                        rows={3}
                      />
                      <div style={{ fontSize: '0.75rem', color: '#6b7280', marginTop: '0.25rem' }}>
                        Valfri beskrivning (max 500 tecken)
                      </div>
                    </div>
                  </>
                )}
              </>
            )}
          </div>

          {/* Modal Footer */}
          <div style={{
            flexShrink: 0,
            borderTop: '1px solid #e5e7eb',
            padding: '1.5rem',
            display: 'flex',
            justifyContent: 'flex-end',
            gap: '1rem'
          }}>
            <button
              onClick={onCancel}
              className="action-button secondary"
              disabled={initiating}
            >
              <span>Avbryt</span>
            </button>
            <button
              onClick={handleInitiateFlow}
              className="action-button primary"
              disabled={!selectedProvider || !tokenName.trim() || initiating}
            >
              <span>{initiating ? 'Initierar...' : 'Starta OAuth2-flöde'}</span>
            </button>
          </div>
        </div>
      </div>
    </Portal>
  )
}
