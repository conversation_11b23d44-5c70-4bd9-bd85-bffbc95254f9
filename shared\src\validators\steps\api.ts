import { RpaStep } from '../../types';
import { ValidationResult, ValidationError, getDefaultVariableName } from '../../utils';
import type {
  FortnoxCreateVoucherStep,
  FortnoxUploadFileStep,
  FortnoxAttachFileToVoucherStep,
  FortnoxUploadAndCreateVoucherStep,
  EAccountingUploadFileStep,
  EAccountingCreateVoucherStep,
  EAccountingAttachFileToVoucherStep,
  EAccountingUploadAndCreateVoucherStep
} from '../../types/steps/api';

/**
 * Validerar Fortnox Create Voucher steg
 */
export function validateFortnoxCreateVoucherStep(step: FortnoxCreateVoucherStep): ValidationResult {
  const errors: ValidationError[] = [];

  // Grundläggande validering
  if (!step.type || step.type !== 'fortnoxCreateVoucher') {
    errors.push({
      field: 'type',
      message: 'Steg-typ måste vara "fortnoxCreateVoucher"',

    });
  }



  // Validera input-variabel
  if (!step.inputVariable || typeof step.inputVariable !== 'string' || step.inputVariable.trim() === '') {
    errors.push({
      field: 'inputVariable',
      message: 'Input-variabel är obligatorisk och måste vara en sträng',
    });
  }

  // Validera AI-prompt
  if (!step.aiPrompt || typeof step.aiPrompt !== 'string' || step.aiPrompt.trim() === '') {
    errors.push({
      field: 'aiPrompt',
      message: 'AI-prompt är obligatorisk och måste vara en sträng',
    });
  }

  // Validera verifikationsserie
  if (step.voucherSeries && !/^[A-Z]$/.test(step.voucherSeries)) {
    errors.push({
      field: 'voucherSeries',
      message: 'Verifikationsserie måste vara en stor bokstav (A-Z)',

    });
  }

  // Validera transaktionsdatum format (om angivet)
  if (step.transactionDate && !isValidDateFormat(step.transactionDate)) {
    errors.push({
      field: 'transactionDate',
      message: 'Transaktionsdatum måste vara i format YYYY-MM-DD eller en variabel',

    });
  }

  // Validera variabelnamn
  if (step.variableName && !/^[a-zA-Z][a-zA-Z0-9_-]*$/.test(step.variableName)) {
    errors.push({
      field: 'variableName',
      message: 'Variabelnamn måste börja med en bokstav och får endast innehålla bokstäver, siffror, bindestreck och understreck',

    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}



/**
 * Kontrollerar om datum är i giltigt format (YYYY-MM-DD eller variabel)
 */
function isValidDateFormat(date: string): boolean {
  // Tillåt variabler (börjar med ${)
  if (date.startsWith('${') && date.endsWith('}')) {
    return true;
  }

  // Kontrollera YYYY-MM-DD format
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  if (!dateRegex.test(date)) {
    return false;
  }

  // Kontrollera att det är ett giltigt datum
  const parsedDate = new Date(date);
  return parsedDate instanceof Date && !isNaN(parsedDate.getTime());
}

/**
 * Validerar Fortnox Upload File steg
 */
export function validateFortnoxUploadFileStep(step: FortnoxUploadFileStep): ValidationResult {
  const errors: ValidationError[] = [];

  // Grundläggande validering
  if (!step.type || step.type !== 'fortnoxUploadFile') {
    errors.push({
      field: 'type',
      message: 'Steg-typ måste vara "fortnoxUploadFile"',
    });
  }

  // Validera input-variabel
  if (!step.inputVariable || typeof step.inputVariable !== 'string' || step.inputVariable.trim() === '') {
    errors.push({
      field: 'inputVariable',
      message: 'Input-variabel är obligatorisk och måste vara en sträng',
    });
  }

  // Validera variabelnamn
  if (step.variableName && !/^[a-zA-Z][a-zA-Z0-9_-]*$/.test(step.variableName)) {
    errors.push({
      field: 'variableName',
      message: 'Variabelnamn måste börja med en bokstav och får endast innehålla bokstäver, siffror, bindestreck och understreck',
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validerar Fortnox Attach File to Voucher steg
 */
export function validateFortnoxAttachFileToVoucherStep(step: FortnoxAttachFileToVoucherStep): ValidationResult {
  const errors: ValidationError[] = [];

  // Grundläggande validering
  if (!step.type || step.type !== 'fortnoxAttachFileToVoucher') {
    errors.push({
      field: 'type',
      message: 'Steg-typ måste vara "fortnoxAttachFileToVoucher"',
    });
  }

  // Validera fil-ID variabel
  if (!step.fileIdVariable || typeof step.fileIdVariable !== 'string' || step.fileIdVariable.trim() === '') {
    errors.push({
      field: 'fileIdVariable',
      message: 'Fil-ID variabel är obligatorisk och måste vara en sträng',
    });
  }

  // Validera verifikationsnummer variabel
  if (!step.voucherNumberVariable || typeof step.voucherNumberVariable !== 'string' || step.voucherNumberVariable.trim() === '') {
    errors.push({
      field: 'voucherNumberVariable',
      message: 'Verifikationsnummer variabel är obligatorisk och måste vara en sträng',
    });
  }

  // Validera variabelnamn
  if (step.variableName && !/^[a-zA-Z][a-zA-Z0-9_-]*$/.test(step.variableName)) {
    errors.push({
      field: 'variableName',
      message: 'Variabelnamn måste börja med en bokstav och får endast innehålla bokstäver, siffror, bindestreck och understreck',
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validerar Fortnox Upload and Create Voucher steg
 */
export function validateFortnoxUploadAndCreateVoucherStep(step: FortnoxUploadAndCreateVoucherStep): ValidationResult {
  const errors: ValidationError[] = [];

  // Grundläggande validering
  if (!step.type || step.type !== 'fortnoxUploadAndCreateVoucher') {
    errors.push({
      field: 'type',
      message: 'Steg-typ måste vara "fortnoxUploadAndCreateVoucher"',
    });
  }

  // Validera fil input-variabel
  if (!step.fileInputVariable || typeof step.fileInputVariable !== 'string' || step.fileInputVariable.trim() === '') {
    errors.push({
      field: 'fileInputVariable',
      message: 'Fil input-variabel är obligatorisk och måste vara en sträng',
    });
  }

  // Validera verifikation input-variabel
  if (!step.voucherInputVariable || typeof step.voucherInputVariable !== 'string' || step.voucherInputVariable.trim() === '') {
    errors.push({
      field: 'voucherInputVariable',
      message: 'Verifikation input-variabel är obligatorisk och måste vara en sträng',
    });
  }

  // Validera AI-prompt
  if (!step.aiPrompt || typeof step.aiPrompt !== 'string' || step.aiPrompt.trim() === '') {
    errors.push({
      field: 'aiPrompt',
      message: 'AI-prompt är obligatorisk och måste vara en sträng',
    });
  }

  // Validera verifikationsserie
  if (step.voucherSeries && !/^[A-Z]$/.test(step.voucherSeries)) {
    errors.push({
      field: 'voucherSeries',
      message: 'Verifikationsserie måste vara en stor bokstav (A-Z)',
    });
  }

  // Validera transaktionsdatum format (om angivet)
  if (step.transactionDate && !isValidDateFormat(step.transactionDate)) {
    errors.push({
      field: 'transactionDate',
      message: 'Transaktionsdatum måste vara i format YYYY-MM-DD eller en variabel',
    });
  }

  // Validera variabelnamn
  if (step.variableName && !/^[a-zA-Z][a-zA-Z0-9_-]*$/.test(step.variableName)) {
    errors.push({
      field: 'variableName',
      message: 'Variabelnamn måste börja med en bokstav och får endast innehålla bokstäver, siffror, bindestreck och understreck',
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Skapar ett nytt Fortnox Create Voucher steg med standardvärden
 */
export function createFortnoxCreateVoucherStep(): FortnoxCreateVoucherStep {
  return {
    id: '',
    type: 'fortnoxCreateVoucher',
    name: 'Skapa Fortnox Verifikation',
    description: 'Skapa verifikation i Fortnox med AI',
    inputVariable: '',
    aiPrompt: 'Skapa en balanserad verifikation baserat på input-data enligt svensk bokföringssed',
    voucherSeries: 'A',
    variableName: getDefaultVariableName('fortnoxCreateVoucher')
  };
}

/**
 * Skapar ett nytt Fortnox Upload File steg med standardvärden
 */
export function createFortnoxUploadFileStep(): FortnoxUploadFileStep {
  return {
    id: '',
    type: 'fortnoxUploadFile',
    name: 'Ladda upp fil till Fortnox',
    description: 'Ladda upp fil till Fortnox arkiv',
    inputVariable: '',
    variableName: getDefaultVariableName('fortnoxUploadFile')
  };
}

/**
 * Skapar ett nytt Fortnox Attach File to Voucher steg med standardvärden
 */
export function createFortnoxAttachFileToVoucherStep(): FortnoxAttachFileToVoucherStep {
  return {
    id: '',
    type: 'fortnoxAttachFileToVoucher',
    name: 'Koppla fil till verifikation',
    description: 'Koppla en uppladdad fil till en verifikation',
    fileIdVariable: '',
    voucherNumberVariable: '',
    variableName: getDefaultVariableName('fortnoxAttachFileToVoucher')
  };
}

/**
 * Skapar ett nytt Fortnox Upload and Create Voucher steg med standardvärden
 */
export function createFortnoxUploadAndCreateVoucherStep(): FortnoxUploadAndCreateVoucherStep {
  return {
    id: '',
    type: 'fortnoxUploadAndCreateVoucher',
    name: 'Ladda upp fil och skapa verifikation',
    description: 'Ladda upp fil och skapa verifikation med automatisk koppling',
    fileInputVariable: '',
    voucherInputVariable: '',
    aiPrompt: 'Skapa en balanserad verifikation baserat på input-data enligt svensk bokföringssed',
    voucherSeries: 'A',
    variableName: getDefaultVariableName('fortnoxUploadAndCreateVoucher')
  };
}

// eAccounting API validators

/**
 * Validerar eAccounting Upload File steg
 */
export function validateEAccountingUploadFileStep(step: EAccountingUploadFileStep): ValidationResult {
  const errors: ValidationError[] = [];

  // Grundläggande validering
  if (!step.type || step.type !== 'eAccountingUploadFile') {
    errors.push({
      field: 'type',
      message: 'Steg-typ måste vara "eAccountingUploadFile"',
    });
  }

  // Validera input-variabel
  if (!step.inputVariable || typeof step.inputVariable !== 'string' || step.inputVariable.trim() === '') {
    errors.push({
      field: 'inputVariable',
      message: 'Input-variabel är obligatorisk och måste vara en sträng',
    });
  }

  // Validera filnamn om angivet
  if (step.filename && step.filename.length > 200) {
    errors.push({
      field: 'filename',
      message: 'Filnamn får inte vara längre än 200 tecken',
    });
  }

  // Validera content type om angivet
  if (step.contentType) {
    const validContentTypes = ['image/jpeg', 'image/png', 'image/tiff', 'application/pdf'];
    if (!validContentTypes.includes(step.contentType)) {
      errors.push({
        field: 'contentType',
        message: 'Content type måste vara en av: ' + validContentTypes.join(', '),
      });
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validerar eAccounting Create Voucher steg
 */
export function validateEAccountingCreateVoucherStep(step: EAccountingCreateVoucherStep): ValidationResult {
  const errors: ValidationError[] = [];

  // Grundläggande validering
  if (!step.type || step.type !== 'eAccountingCreateVoucher') {
    errors.push({
      field: 'type',
      message: 'Steg-typ måste vara "eAccountingCreateVoucher"',
    });
  }

  // Validera input-variabel
  if (!step.inputVariable || typeof step.inputVariable !== 'string' || step.inputVariable.trim() === '') {
    errors.push({
      field: 'inputVariable',
      message: 'Input-variabel är obligatorisk och måste vara en sträng',
    });
  }

  // Validera AI-prompt
  if (!step.aiPrompt || typeof step.aiPrompt !== 'string' || step.aiPrompt.trim() === '') {
    errors.push({
      field: 'aiPrompt',
      message: 'AI-prompt är obligatorisk och måste vara en sträng',
    });
  }

  // Validera voucher text om angivet
  if (step.voucherText && step.voucherText.length > 1000) {
    errors.push({
      field: 'voucherText',
      message: 'Verifikationstext får inte vara längre än 1000 tecken',
    });
  }

  // Validera datum format om angivet
  if (step.voucherDate && !/^\d{4}-\d{2}-\d{2}$/.test(step.voucherDate)) {
    errors.push({
      field: 'voucherDate',
      message: 'Verifikationsdatum måste vara i format yyyy-mm-dd',
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validerar eAccounting Attach File to Voucher steg
 */
export function validateEAccountingAttachFileToVoucherStep(step: EAccountingAttachFileToVoucherStep): ValidationResult {
  const errors: ValidationError[] = [];

  // Grundläggande validering
  if (!step.type || step.type !== 'eAccountingAttachFileToVoucher') {
    errors.push({
      field: 'type',
      message: 'Steg-typ måste vara "eAccountingAttachFileToVoucher"',
    });
  }

  // Validera attachment ID-variabel
  if (!step.attachmentIdVariable || typeof step.attachmentIdVariable !== 'string' || step.attachmentIdVariable.trim() === '') {
    errors.push({
      field: 'attachmentIdVariable',
      message: 'Attachment ID-variabel är obligatorisk och måste vara en sträng',
    });
  }

  // Validera voucher ID-variabel
  if (!step.voucherIdVariable || typeof step.voucherIdVariable !== 'string' || step.voucherIdVariable.trim() === '') {
    errors.push({
      field: 'voucherIdVariable',
      message: 'Voucher ID-variabel är obligatorisk och måste vara en sträng',
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validerar eAccounting Upload and Create Voucher steg
 */
export function validateEAccountingUploadAndCreateVoucherStep(step: EAccountingUploadAndCreateVoucherStep): ValidationResult {
  const errors: ValidationError[] = [];

  // Grundläggande validering
  if (!step.type || step.type !== 'eAccountingUploadAndCreateVoucher') {
    errors.push({
      field: 'type',
      message: 'Steg-typ måste vara "eAccountingUploadAndCreateVoucher"',
    });
  }

  // Validera fil input-variabel
  if (!step.fileInputVariable || typeof step.fileInputVariable !== 'string' || step.fileInputVariable.trim() === '') {
    errors.push({
      field: 'fileInputVariable',
      message: 'Fil input-variabel är obligatorisk och måste vara en sträng',
    });
  }

  // Validera verifikation input-variabel
  if (!step.voucherInputVariable || typeof step.voucherInputVariable !== 'string' || step.voucherInputVariable.trim() === '') {
    errors.push({
      field: 'voucherInputVariable',
      message: 'Verifikation input-variabel är obligatorisk och måste vara en sträng',
    });
  }

  // Validera AI-prompt
  if (!step.aiPrompt || typeof step.aiPrompt !== 'string' || step.aiPrompt.trim() === '') {
    errors.push({
      field: 'aiPrompt',
      message: 'AI-prompt är obligatorisk och måste vara en sträng',
    });
  }

  // Validera filnamn om angivet
  if (step.filename && step.filename.length > 200) {
    errors.push({
      field: 'filename',
      message: 'Filnamn får inte vara längre än 200 tecken',
    });
  }

  // Validera content type om angivet
  if (step.contentType) {
    const validContentTypes = ['image/jpeg', 'image/png', 'image/tiff', 'application/pdf'];
    if (!validContentTypes.includes(step.contentType)) {
      errors.push({
        field: 'contentType',
        message: 'Content type måste vara en av: ' + validContentTypes.join(', '),
      });
    }
  }

  // Validera voucher text om angivet
  if (step.voucherText && step.voucherText.length > 1000) {
    errors.push({
      field: 'voucherText',
      message: 'Verifikationstext får inte vara längre än 1000 tecken',
    });
  }

  // Validera datum format om angivet
  if (step.voucherDate && !/^\d{4}-\d{2}-\d{2}$/.test(step.voucherDate)) {
    errors.push({
      field: 'voucherDate',
      message: 'Verifikationsdatum måste vara i format yyyy-mm-dd',
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Skapar ett nytt eAccounting Upload File steg med standardvärden
 */
export function createEAccountingUploadFileStep(): EAccountingUploadFileStep {
  return {
    id: '',
    type: 'eAccountingUploadFile',
    name: 'Ladda upp fil till eAccounting',
    description: 'Ladda upp fil till eAccounting Attachments',
    inputVariable: '',
    variableName: getDefaultVariableName('eAccountingUploadFile')
  };
}

/**
 * Skapar ett nytt eAccounting Create Voucher steg med standardvärden
 */
export function createEAccountingCreateVoucherStep(): EAccountingCreateVoucherStep {
  return {
    id: '',
    type: 'eAccountingCreateVoucher',
    name: 'Skapa verifikation i eAccounting',
    description: 'Skapa verifikation i eAccounting med AI-assistans',
    inputVariable: '',
    aiPrompt: 'Skapa en balanserad verifikation baserat på input-data enligt svensk bokföringssed',
    variableName: getDefaultVariableName('eAccountingCreateVoucher')
  };
}

/**
 * Skapar ett nytt eAccounting Attach File to Voucher steg med standardvärden
 */
export function createEAccountingAttachFileToVoucherStep(): EAccountingAttachFileToVoucherStep {
  return {
    id: '',
    type: 'eAccountingAttachFileToVoucher',
    name: 'Koppla fil till verifikation',
    description: 'Koppla en uppladdad fil till en verifikation i eAccounting',
    attachmentIdVariable: '',
    voucherIdVariable: '',
    variableName: getDefaultVariableName('eAccountingAttachFileToVoucher')
  };
}

/**
 * Skapar ett nytt eAccounting Upload and Create Voucher steg med standardvärden
 */
export function createEAccountingUploadAndCreateVoucherStep(): EAccountingUploadAndCreateVoucherStep {
  return {
    id: '',
    type: 'eAccountingUploadAndCreateVoucher',
    name: 'Ladda upp fil och skapa verifikation',
    description: 'Ladda upp fil och skapa verifikation med automatisk koppling i eAccounting',
    fileInputVariable: '',
    voucherInputVariable: '',
    aiPrompt: 'Skapa en balanserad verifikation baserat på input-data enligt svensk bokföringssed',
    variableName: getDefaultVariableName('eAccountingUploadAndCreateVoucher')
  };
}

// Huvudvalidator för API-steg
export function validateApiStep(step: RpaStep): ValidationResult {
  switch ((step as any).type) {
    case 'fortnoxCreateVoucher':
      return validateFortnoxCreateVoucherStep(step as unknown as FortnoxCreateVoucherStep);
    case 'fortnoxUploadFile':
      return validateFortnoxUploadFileStep(step as unknown as FortnoxUploadFileStep);
    case 'fortnoxAttachFileToVoucher':
      return validateFortnoxAttachFileToVoucherStep(step as unknown as FortnoxAttachFileToVoucherStep);
    case 'fortnoxUploadAndCreateVoucher':
      return validateFortnoxUploadAndCreateVoucherStep(step as unknown as FortnoxUploadAndCreateVoucherStep);
    case 'eAccountingUploadFile':
      return validateEAccountingUploadFileStep(step as unknown as EAccountingUploadFileStep);
    case 'eAccountingCreateVoucher':
      return validateEAccountingCreateVoucherStep(step as unknown as EAccountingCreateVoucherStep);
    case 'eAccountingAttachFileToVoucher':
      return validateEAccountingAttachFileToVoucherStep(step as unknown as EAccountingAttachFileToVoucherStep);
    case 'eAccountingUploadAndCreateVoucher':
      return validateEAccountingUploadAndCreateVoucherStep(step as unknown as EAccountingUploadAndCreateVoucherStep);
    default:
      return {
        isValid: false,
        errors: [{
          field: 'type',
          message: `Okänd API steg-typ: ${step.type}`,

        }]
      };
  }
}

export function createApiStepFromType(stepType: string): any {
  switch (stepType) {
    case 'fortnoxCreateVoucher':
      return createFortnoxCreateVoucherStep();
    case 'fortnoxUploadFile':
      return createFortnoxUploadFileStep();
    case 'fortnoxAttachFileToVoucher':
      return createFortnoxAttachFileToVoucherStep();
    case 'fortnoxUploadAndCreateVoucher':
      return createFortnoxUploadAndCreateVoucherStep();
    case 'eAccountingUploadFile':
      return createEAccountingUploadFileStep();
    case 'eAccountingCreateVoucher':
      return createEAccountingCreateVoucherStep();
    case 'eAccountingAttachFileToVoucher':
      return createEAccountingAttachFileToVoucherStep();
    case 'eAccountingUploadAndCreateVoucher':
      return createEAccountingUploadAndCreateVoucherStep();
    default:
      throw new Error(`Okänd API steg-typ: ${stepType}`);
  }
}
