# LLM Provider Felsökningsguide

## Översikt

Denna guide hj<PERSON><PERSON>per dig att diagnostisera och lösa problem med LLM provider-arkitekturen i RPA-applikationen.

## Snabb Diagnostik

### 1. Health Check
Börja alltid med att kontrollera AI Assistant status:

```bash
curl http://localhost:3002/api/ai-assistant/health
```

**Möjliga svar och betydelser:**

```json
// ✅ Allt fungerar
{
  "success": true,
  "data": {
    "aiStatus": "connected",
    "provider": "openai",
    "hasApiKey": true,
    "error": null
  }
}

// ❌ Problem med API-nyckel
{
  "success": true,
  "data": {
    "aiStatus": "error",
    "provider": "openai",
    "hasApiKey": false,
    "error": "Invalid API key"
  }
}

// ❌ Provider inte konfigurerad
{
  "success": true,
  "data": {
    "aiStatus": "error",
    "provider": "azure",
    "hasApiKey": false,
    "error": "AZURE_OPENAI_API_KEY environment variable is required"
  }
}
```

### 2. Kontrollera Miljövariabler

```bash
# Kontrollera grundläggande konfiguration
echo "Provider: $LLM_PROVIDER"
echo "Default Model: $LLM_DEFAULT_MODEL"
echo "Fallback Model: $LLM_FALLBACK_MODEL"

# För OpenAI
echo "OpenAI Key: ${OPENAI_API_KEY:0:10}..." # Visa bara början

# För Azure
echo "Azure Key: ${AZURE_OPENAI_API_KEY:0:10}..."
echo "Azure Endpoint: $AZURE_OPENAI_ENDPOINT"
echo "Azure Version: $AZURE_OPENAI_API_VERSION"
```

## Vanliga Problem och Lösningar

### Problem 1: "LLM provider not properly configured"

**Symptom:**
- Health check visar `aiStatus: "error"`
- AI Assistant funktioner fungerar inte
- Felmeddelande i server logs

**Möjliga orsaker:**
1. Saknade miljövariabler
2. Felaktiga API-nycklar
3. Fel provider-konfiguration

**Lösning:**

```bash
# 1. Kontrollera att .env-filen finns
ls -la backend/.env

# 2. Kontrollera provider-konfiguration
grep LLM_PROVIDER backend/.env

# 3. För OpenAI provider
grep OPENAI_API_KEY backend/.env

# 4. För Azure provider
grep AZURE_OPENAI backend/.env

# 5. Starta om servern efter ändringar
npm run dev
```

### Problem 2: "Model not supported by provider"

**Symptom:**
- Specifika modeller fungerar inte
- Fallback till annan modell
- Varningsmeddelanden i logs

**Möjliga orsaker:**
1. Modell inte tillgänglig för provider
2. Felstavad modellnamn
3. Azure deployment inte konfigurerat

**Lösning:**

```bash
# Kontrollera tillgängliga modeller för OpenAI
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
     https://api.openai.com/v1/models | grep -E "(gpt-4o-mini|o1-mini)"

# För Azure - kontrollera deployments i Azure Portal
# Eller testa direkt:
curl -H "api-key: $AZURE_OPENAI_API_KEY" \
     "$AZURE_OPENAI_ENDPOINT/openai/deployments?api-version=$AZURE_OPENAI_API_VERSION"
```

### Problem 3: "API rate limit exceeded"

**Symptom:**
- Intermittenta fel
- Långsamma svar
- 429 HTTP-fel i logs

**Lösning:**

```bash
# 1. Kontrollera API-användning
# OpenAI: https://platform.openai.com/usage
# Azure: Azure Portal -> Metrics

# 2. Implementera retry-logik (redan inbyggt)
# 3. Överväg att uppgradera API-plan
# 4. Använd billigare modell för utveckling
LLM_DEFAULT_MODEL=gpt-4o-mini
```

### Problem 4: "Connection timeout"

**Symptom:**
- Långsamma eller misslyckade requests
- Timeout-fel i logs
- Intermittenta problem

**Lösning:**

```bash
# 1. Testa nätverksanslutning
ping api.openai.com
ping your-resource.openai.azure.com

# 2. Kontrollera firewall/proxy-inställningar
# 3. Testa med curl
curl -w "@curl-format.txt" -o /dev/null -s \
     -H "Authorization: Bearer $OPENAI_API_KEY" \
     https://api.openai.com/v1/models

# 4. Överväg att öka timeout-värden i koden
```

### Problem 5: "Invalid JSON response from AI"

**Symptom:**
- AI-genererade flöden fungerar inte
- Parsing-fel i logs
- Ofullständiga eller korrupta svar

**Lösning:**

```bash
# 1. Kontrollera modellkonfiguration
# Vissa modeller kan ge inkonsistenta svar

# 2. Testa med enklare prompts
curl -X POST http://localhost:3002/api/ai-assistant/generate-flow \
     -H "Content-Type: application/json" \
     -d '{"prompt": "Navigera till Google"}'

# 3. Kontrollera temperature-inställningar
# Lägre temperature = mer konsistenta svar
```

## Provider-specifika Problem

### OpenAI Provider

#### Problem: "Invalid API key"
```bash
# Kontrollera API-nyckel format
echo $OPENAI_API_KEY | grep -E "^sk-proj-[A-Za-z0-9]+"

# Testa direkt mot OpenAI API
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
     https://api.openai.com/v1/models
```

#### Problem: "Organization not found"
```bash
# Om du använder organization ID
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
     -H "OpenAI-Organization: org-your-org-id" \
     https://api.openai.com/v1/models
```

### Azure Provider

#### Problem: "Resource not found"
```bash
# Kontrollera endpoint format
echo $AZURE_OPENAI_ENDPOINT
# Ska vara: https://your-resource.openai.azure.com/

# Testa endpoint
curl -H "api-key: $AZURE_OPENAI_API_KEY" \
     "$AZURE_OPENAI_ENDPOINT/openai/deployments?api-version=$AZURE_OPENAI_API_VERSION"
```

#### Problem: "Deployment not found"
```bash
# Kontrollera deployment-namn
echo $AZURE_MODEL_GPT4O_MINI

# Lista alla deployments
curl -H "api-key: $AZURE_OPENAI_API_KEY" \
     "$AZURE_OPENAI_ENDPOINT/openai/deployments?api-version=$AZURE_OPENAI_API_VERSION"
```

## Debugging-verktyg

### 1. Detaljerad Logging

Aktivera debug-logging i utvecklingsmiljö:

```bash
# I backend/.env
NODE_ENV=development
DEBUG=llm:*

# Starta servern
npm run dev
```

### 2. Test-script

Skapa ett test-script för att validera konfiguration:

```bash
#!/bin/bash
# test-llm-config.sh

echo "=== LLM Provider Configuration Test ==="

# Kontrollera miljövariabler
echo "Provider: $LLM_PROVIDER"
echo "Default Model: $LLM_DEFAULT_MODEL"

# Testa health endpoint
echo "=== Health Check ==="
curl -s http://localhost:3002/api/ai-assistant/health | jq .

# Testa enkel AI-funktion
echo "=== Simple AI Test ==="
curl -s -X POST http://localhost:3002/api/ai-assistant/generate-flow \
     -H "Content-Type: application/json" \
     -d '{"prompt": "Navigera till Google"}' | jq .success
```

### 3. Provider-byte Test

```bash
#!/bin/bash
# test-provider-switch.sh

echo "Testing provider switch..."

# Spara nuvarande provider
ORIGINAL_PROVIDER=$LLM_PROVIDER

# Testa OpenAI
export LLM_PROVIDER=openai
echo "Testing OpenAI..."
curl -s http://localhost:3002/api/ai-assistant/health | jq .data.provider

# Testa Azure (om konfigurerat)
export LLM_PROVIDER=azure
echo "Testing Azure..."
curl -s http://localhost:3002/api/ai-assistant/health | jq .data.provider

# Återställ
export LLM_PROVIDER=$ORIGINAL_PROVIDER
```

## Performance-diagnostik

### 1. Response Time Monitoring

```bash
# Mät svarstider
time curl -X POST http://localhost:3002/api/ai-assistant/generate-flow \
          -H "Content-Type: application/json" \
          -d '{"prompt": "Skapa ett enkelt flöde"}'
```

### 2. Token Usage Tracking

Övervaka token-användning för kostnadsoptimering:

```bash
# Kontrollera usage i OpenAI Dashboard
# Eller implementera lokal tracking i applikationen
```

## Vanliga Konfigurationsfel

### 1. Fel .env-format
```bash
# ❌ Fel - mellanslag runt =
LLM_PROVIDER = openai

# ✅ Rätt - inga mellanslag
LLM_PROVIDER=openai
```

### 2. Fel API-nyckel format
```bash
# ❌ Fel - gammal format
OPENAI_API_KEY=sk-abc123

# ✅ Rätt - nytt format
OPENAI_API_KEY=sk-proj-abc123...
```

### 3. Fel Azure endpoint
```bash
# ❌ Fel - saknar https://
AZURE_OPENAI_ENDPOINT=your-resource.openai.azure.com

# ✅ Rätt - komplett URL
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
```

## Support och Hjälp

### Loggar att kontrollera
1. **Server logs**: `npm run dev` output
2. **Browser console**: För frontend-fel
3. **Network tab**: För API-anrop
4. **Health endpoint**: För provider-status

### Kontaktinformation
- **Teknisk support**: [Lägg till kontaktinfo]
- **Dokumentation**: `docs/user-guide/`
- **GitHub Issues**: [Lägg till repo-länk]

### Eskalering
Om problemet kvarstår efter denna guide:
1. Samla in alla relevanta loggar
2. Dokumentera steg för att reproducera
3. Inkludera miljökonfiguration (utan API-nycklar)
4. Kontakta teknisk support
