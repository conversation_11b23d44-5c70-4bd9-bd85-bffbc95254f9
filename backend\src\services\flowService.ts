import { RpaFlow, generateId } from '@rpa-project/shared';
import { statements } from '../database/database';

// SQLite-based storage
class FlowStorage {
  async save(flow: RpaFlow): Promise<RpaFlow> {
    const flowData = {
      id: flow.id,
      name: flow.name,
      description: flow.description || '',
      customer_id: flow.customerId,
      steps: JSON.stringify(flow.steps),
      variables: JSON.stringify(flow.variables || {}),
      settings: JSON.stringify(flow.settings || {}),
      created_at: flow.createdAt.toISOString(),
      updated_at: flow.updatedAt.toISOString()
    };

    try {
      // Try to update first
      const updateResult = statements.updateFlow.run(
        flowData.name,
        flowData.description,
        flowData.customer_id,
        flowData.steps,
        flowData.variables,
        flowData.settings,
        flowData.updated_at,
        flowData.id
      );

      // If no rows were affected, insert new flow
      if (updateResult.changes === 0) {
        statements.insertFlow.run(
          flowData.id,
          flowData.name,
          flowData.description,
          flowData.customer_id,
          flowData.steps,
          flowData.variables,
          flowData.settings,
          flowData.created_at,
          flowData.updated_at
        );
      }

      console.log(`💾 Saved flow: ${flow.id} - ${flow.name}`);
      return flow;
    } catch (error) {
      console.error(`❌ Failed to save flow ${flow.id}:`, error);
      throw error;
    }
  }

  async findById(id: string): Promise<RpaFlow | null> {
    try {
      const row = statements.getFlowById.get(id) as any;

      if (!row) {
        console.log(`🔍 Flow not found: ${id}`);
        return null;
      }

      const flow: RpaFlow = {
        id: row.id,
        name: row.name,
        description: row.description,
        customerId: row.customer_id,
        steps: JSON.parse(row.steps),
        variables: JSON.parse(row.variables || '{}'),
        settings: JSON.parse(row.settings || '{}'),
        createdAt: new Date(row.created_at),
        updatedAt: new Date(row.updated_at)
      };

      console.log(`✅ Found flow: ${id} - ${flow.name}`);
      return flow;
    } catch (error) {
      console.error(`❌ Failed to find flow ${id}:`, error);
      throw error;
    }
  }

  async findAll(options?: { limit?: number; offset?: number }): Promise<RpaFlow[]> {
    try {
      let rows: any[];

      if (options?.limit !== undefined) {
        // Use pagination
        const limit = options.limit;
        const offset = options.offset || 0;
        rows = statements.getAllFlowsPaginated.all(limit, offset) as any[];
      } else {
        // Get all flows (backward compatibility)
        rows = statements.getAllFlows.all() as any[];
      }

      const flows = rows.map(row => ({
        id: row.id,
        name: row.name,
        description: row.description,
        customerId: row.customer_id,
        steps: JSON.parse(row.steps),
        variables: JSON.parse(row.variables || '{}'),
        settings: JSON.parse(row.settings || '{}'),
        createdAt: new Date(row.created_at),
        updatedAt: new Date(row.updated_at)
      }));

      console.log(`📋 Found ${flows.length} flows in database`);
      return flows;
    } catch (error) {
      console.error('❌ Failed to get all flows:', error);
      throw error;
    }
  }

  async delete(id: string): Promise<boolean> {
    try {
      const result = statements.deleteFlow.run(id);
      const deleted = result.changes > 0;
      console.log(`🗑️ Deleted flow: ${id} - ${deleted ? 'SUCCESS' : 'NOT FOUND'}`);
      return deleted;
    } catch (error) {
      console.error(`❌ Failed to delete flow ${id}:`, error);
      throw error;
    }
  }

  async exists(id: string): Promise<boolean> {
    try {
      const result = statements.flowExists.get(id);
      return !!result;
    } catch (error) {
      console.error(`❌ Failed to check if flow exists ${id}:`, error);
      throw error;
    }
  }
}

// Singleton storage instance
const globalFlowStorage = new FlowStorage();

export class FlowService {
  private storage = globalFlowStorage;

  async getAllFlows(options?: { limit?: number; offset?: number }): Promise<RpaFlow[]> {
    return this.storage.findAll(options);
  }

  async getFlowById(id: string): Promise<RpaFlow | null> {
    return this.storage.findById(id);
  }

  async createFlow(flow: RpaFlow): Promise<RpaFlow> {
    // Generate ID if not provided
    if (!flow.id) {
      flow.id = generateId();
    }

    // Ensure timestamps
    const now = new Date();
    flow.createdAt = now;
    flow.updatedAt = now;

    return this.storage.save(flow);
  }

  async updateFlow(id: string, flow: RpaFlow): Promise<RpaFlow> {
    const exists = await this.storage.exists(id);
    if (!exists) {
      throw new Error('Flow not found');
    }

    flow.id = id;
    flow.updatedAt = new Date();

    return this.storage.save(flow);
  }

  async deleteFlow(id: string): Promise<void> {
    const deleted = await this.storage.delete(id);
    if (!deleted) {
      throw new Error('Flow not found');
    }
  }

  async flowExists(id: string): Promise<boolean> {
    return this.storage.exists(id);
  }

  // Utility methods
  async duplicateFlow(id: string, newName?: string): Promise<RpaFlow> {
    const originalFlow = await this.getFlowById(id);
    if (!originalFlow) {
      throw new Error('Flow not found');
    }

    const duplicatedFlow: RpaFlow = {
      ...originalFlow,
      id: generateId(),
      name: newName || `${originalFlow.name} (Copy)`,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    return this.createFlow(duplicatedFlow);
  }

  async getFlowsByName(name: string): Promise<RpaFlow[]> {
    const allFlows = await this.getAllFlows();
    return allFlows.filter(flow =>
      flow.name.toLowerCase().includes(name.toLowerCase())
    );
  }
}

// Export singleton instance
export const flowService = new FlowService();
