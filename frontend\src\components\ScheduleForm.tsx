import React, { useState, useEffect } from 'react'
import {
  FlowSchedule,
  CreateScheduleRequest,
  UpdateScheduleRequest,
  Rpa<PERSON>low,
  Customer,
  COMMON_CRON_EXPRESSIONS,
  CronExpressionInfo
} from '@rpa-project/shared'
import { scheduleApi, flowApi, customerApi } from '../services/api'

interface ScheduleFormProps {
  schedule?: FlowSchedule
  flowId?: string
  onSave: (schedule: FlowSchedule) => void
  onCancel: () => void
}

export const ScheduleForm: React.FC<ScheduleFormProps> = ({
  schedule,
  flowId,
  onSave,
  onCancel
}) => {
  const [formData, setFormData] = useState({
    flowId: flowId || schedule?.flowId || '',
    name: schedule?.name || '',
    description: schedule?.description || '',
    cronExpression: schedule?.cronExpression || '0 9 * * 1-5',
    timezone: schedule?.timezone || 'Europe/Stockholm',
    enabled: schedule?.enabled !== false,
    variables: schedule?.variables || {}
  })

  const [flows, setFlows] = useState<RpaFlow[]>([])
  const [customers, setCustomers] = useState<Customer[]>([])
  const [cronInfo, setCronInfo] = useState<CronExpressionInfo | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [validatingCron, setValidatingCron] = useState(false)
  const [flowSearch, setFlowSearch] = useState('')
  const [showFlowDropdown, setShowFlowDropdown] = useState(false)

  useEffect(() => {
    loadFlows()
    loadCustomers()
  }, [flowId])

  useEffect(() => {
    validateCronExpression()
  }, [formData.cronExpression, formData.timezone])

  // Auto-set schedule name when flowId is provided and data is loaded
  useEffect(() => {
    if (flowId && flows.length > 0 && customers.length > 0 && !formData.name) {
      const selectedFlow = flows.find(f => f.id === flowId)
      if (selectedFlow) {
        const customer = customers.find(c => c.id === selectedFlow.customerId)
        const autoName = customer ? `${customer.name} - ${selectedFlow.name}` : selectedFlow.name
        setFormData(prev => ({
          ...prev,
          name: autoName
        }))
      }
    }
  }, [flowId, flows, customers, formData.name])

  // Filter flows based on search
  const filteredFlows = flows.filter(flow => {
    if (!flowSearch.trim()) return true

    const query = flowSearch.toLowerCase()
    const customer = customers.find(c => c.id === flow.customerId)

    // Search in flow name
    if (flow.name.toLowerCase().includes(query)) return true

    // Search in flow description
    if (flow.description?.toLowerCase().includes(query)) return true

    // Search in customer name
    if (customer && customer.name.toLowerCase().includes(query)) return true

    return false
  })

  const loadFlows = async () => {
    try {
      const response = await flowApi.getFlows()
      if (response.success) {
        setFlows(response.data || [])
      }
    } catch (err) {
      console.error('Error loading flows:', err)
    }
  }

  const loadCustomers = async () => {
    try {
      const response = await customerApi.getCustomers()
      if (response.success) {
        setCustomers(response.data || [])
      }
    } catch (err) {
      console.error('Error loading customers:', err)
    }
  }

  const validateCronExpression = async () => {
    if (!formData.cronExpression.trim()) {
      setCronInfo(null)
      return
    }

    try {
      setValidatingCron(true)
      const response = await scheduleApi.validateCronExpression(
        formData.cronExpression, 
        formData.timezone
      )
      
      if (response.success) {
        setCronInfo(response.data!)
      }
    } catch (err) {
      console.error('Error validating cron expression:', err)
      setCronInfo({
        expression: formData.cronExpression,
        description: '',
        nextRuns: [],
        isValid: false,
        error: 'Failed to validate expression'
      })
    } finally {
      setValidatingCron(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!cronInfo?.isValid) {
      setError('Please enter a valid cron expression')
      return
    }

    try {
      setLoading(true)
      setError(null)

      if (schedule) {
        // Update existing schedule
        const updateRequest: UpdateScheduleRequest = {
          name: formData.name,
          description: formData.description,
          cronExpression: formData.cronExpression,
          timezone: formData.timezone,
          enabled: formData.enabled,
          variables: formData.variables
        }
        
        const response = await scheduleApi.updateSchedule(schedule.id, updateRequest)
        if (response.success) {
          onSave(response.data!)
        } else {
          setError(response.error || 'Failed to update schedule')
        }
      } else {
        // Create new schedule
        const createRequest: CreateScheduleRequest = {
          flowId: formData.flowId,
          name: formData.name,
          description: formData.description,
          cronExpression: formData.cronExpression,
          timezone: formData.timezone,
          enabled: formData.enabled,
          variables: formData.variables
        }
        
        const response = await scheduleApi.createSchedule(createRequest)
        if (response.success) {
          onSave(response.data!)
        } else {
          setError(response.error || 'Failed to create schedule')
        }
      }
    } catch (err) {
      setError('An error occurred while saving the schedule')
      console.error('Error saving schedule:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleCronPresetSelect = (expression: string) => {
    setFormData({ ...formData, cronExpression: expression })
  }

  const handleFlowSelect = (flow: RpaFlow) => {
    const customer = customers.find(c => c.id === flow.customerId)
    const autoName = customer ? `${customer.name} - ${flow.name}` : flow.name

    setFormData({
      ...formData,
      flowId: flow.id,
      name: formData.name || autoName // Only set auto name if name is empty
    })
    setFlowSearch('')
    setShowFlowDropdown(false)
  }

  // Get selected flow for display
  const selectedFlow = flows.find(f => f.id === formData.flowId)
  const selectedFlowCustomer = selectedFlow ? customers.find(c => c.id === selectedFlow.customerId) : null

  // Swedish translations for common cron expressions
  const cronExpressionTranslations: Record<string, string> = {
    'Every minute': 'Varje minut',
    'Every 5 minutes': 'Var 5:e minut',
    'Every 15 minutes': 'Var 15:e minut',
    'Every 30 minutes': 'Var 30:e minut',
    'Every hour': 'Varje timme',
    'Every 2 hours': 'Var 2:a timme',
    'Every 6 hours': 'Var 6:e timme',
    'Every 12 hours': 'Var 12:e timme',
    'Daily at 9 AM': 'Dagligen kl 09:00',
    'Daily at 6 PM': 'Dagligen kl 18:00',
    'Weekdays at 9 AM': 'Vardagar kl 09:00',
    'Weekends at 10 AM': 'Helger kl 10:00',
    'Weekly on Monday at 9 AM': 'Måndagar kl 09:00',
    'Monthly on 1st at 9 AM': 'Månadsvis den 1:a kl 09:00',
    'Yearly on Jan 1st at 9 AM': 'Årligen 1 jan kl 09:00'
  }

  const timezones = [
    'Europe/Stockholm',
    'UTC',
    'Europe/London',
    'Europe/Paris',
    'America/New_York',
    'America/Chicago',
    'America/Denver',
    'America/Los_Angeles',
    'Asia/Tokyo',
    'Asia/Shanghai',
    'Australia/Sydney'
  ]

  return (
    <div style={{ padding: '1.5rem' }}>
      {/* Error Display */}
      {error && (
        <div className="error-card" style={{ marginBottom: '1rem' }}>
          <h3 className="error-title">Fel</h3>
          <p className="error-message">{error}</p>
        </div>
      )}

      <form onSubmit={handleSubmit}>
        {/* Basic Information */}
        <div className="form-group">
          {!flowId && (
            <div className="form-group">
              <label className="form-label">Flöde *</label>
              <div style={{ position: 'relative' }}>
                <input
                  type="text"
                  className="form-input"
                  value={selectedFlow
                    ? `${selectedFlow.name}${selectedFlowCustomer ? ` (${selectedFlowCustomer.name})` : ''}`
                    : flowSearch}
                  onChange={(e) => {
                    setFlowSearch(e.target.value)
                    setShowFlowDropdown(true)
                    if (!selectedFlow) {
                      // Clear selection if typing and no flow selected
                      setFormData({ ...formData, flowId: '' })
                    }
                  }}
                  onFocus={() => {
                    setShowFlowDropdown(true)
                    if (selectedFlow) {
                      setFlowSearch('')
                    }
                  }}
                  onBlur={() => {
                    // Delay hiding dropdown to allow for clicks
                    setTimeout(() => setShowFlowDropdown(false), 200)
                  }}
                  placeholder="Sök efter flöde..."
                  style={{
                    fontSize: '1rem',
                    borderColor: (formData.flowId?.trim() || '') === '' ? '#ef4444' : '#d1d5db'
                  }}
                  required
                />

                {showFlowDropdown && filteredFlows.length > 0 && (
                  <div style={{
                    position: 'absolute',
                    top: '100%',
                    left: 0,
                    right: 0,
                    backgroundColor: '#ffffff',
                    border: '1px solid #d1d5db',
                    borderTop: 'none',
                    borderRadius: '0 0 0.375rem 0.375rem',
                    maxHeight: '200px',
                    overflowY: 'auto',
                    zIndex: 1000,
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                  }}>
                    {filteredFlows.map((flow) => {
                      const customer = customers.find(c => c.id === flow.customerId)
                      return (
                        <div
                          key={flow.id}
                          onClick={() => handleFlowSelect(flow)}
                          style={{
                            padding: '0.75rem',
                            cursor: 'pointer',
                            borderBottom: '1px solid #f3f4f6',
                            backgroundColor: flow.id === formData.flowId ? '#f9fafb' : 'transparent'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor = '#f9fafb'
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor = flow.id === formData.flowId ? '#f9fafb' : 'transparent'
                          }}
                        >
                          <div style={{ fontWeight: 500 }}>{flow.name}</div>
                          {customer && (
                            <div style={{ fontSize: '0.75rem', color: '#6b7280' }}>
                              {customer.name}
                            </div>
                          )}
                        </div>
                      )
                    })}
                  </div>
                )}
              </div>
            </div>
          )}

          <div className="form-group">
            <label className="form-label">Schemanamn *</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="form-input"
              placeholder="Ange schemanamn"
              required
            />
          </div>

          <div className="form-group">
            <label className="form-label">Beskrivning</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="form-input form-textarea"
              rows={3}
              placeholder="Ange beskrivning (valfritt)"
            />
          </div>
        </div>

        {/* Schedule Configuration */}
        <div className="form-group">
          <label className="form-label">Schema (Cron-uttryck) *</label>

          {/* Common presets */}
          <div style={{ marginBottom: '1rem' }}>
            <p style={{ fontSize: '0.875rem', color: '#6b7280', marginBottom: '0.5rem' }}>
              Vanliga scheman:
            </p>
            <div className="choice-buttons">
              {Object.entries(COMMON_CRON_EXPRESSIONS).map(([name, expression]) => (
                <button
                  key={name}
                  type="button"
                  onClick={() => handleCronPresetSelect(expression)}
                  className={`action-button ${
                    formData.cronExpression === expression ? 'primary' : 'secondary'
                  }`}
                  style={{ fontSize: '0.75rem', padding: '0.375rem 0.75rem', height: 'auto' }}
                >
                  {cronExpressionTranslations[name] || name}
                </button>
              ))}
            </div>
          </div>

          <input
            type="text"
            value={formData.cronExpression}
            onChange={(e) => setFormData({ ...formData, cronExpression: e.target.value })}
            className="form-input"
            placeholder="0 9 * * 1-5"
            required
            style={{
              fontFamily: 'monospace',
              borderColor: cronInfo?.isValid === false ? '#ef4444' : '#d1d5db'
            }}
          />

          {validatingCron && (
            <p style={{
              fontSize: '0.875rem',
              color: '#6b7280',
              marginTop: '0.5rem',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              <span>🔄</span>
              Validerar...
            </p>
          )}

          {cronInfo && (
            <div style={{
              marginTop: '1rem',
              padding: '1rem',
              borderRadius: '0.5rem',
              backgroundColor: cronInfo.isValid ? '#f0fdf4' : '#fef2f2',
              border: `1px solid ${cronInfo.isValid ? '#bbf7d0' : '#fecaca'}`,
              borderLeft: `4px solid ${cronInfo.isValid ? '#10b981' : '#ef4444'}`
            }}>
              {cronInfo.isValid ? (
                <div>
                  <p style={{
                    fontSize: '0.875rem',
                    color: '#065f46',
                    fontWeight: '600',
                    marginBottom: '0.5rem'
                  }}>
                    ✅ {cronInfo.description}
                  </p>
                  <p style={{
                    fontSize: '0.875rem',
                    color: '#047857',
                    marginBottom: '0.5rem'
                  }}>
                    🔜 Nästa körningar:
                  </p>
                  <ul style={{ fontSize: '0.875rem', color: '#047857', marginLeft: '1rem' }}>
                    {cronInfo.nextRuns.slice(0, 3).map((date, index) => (
                      <li key={index} style={{ marginBottom: '0.25rem' }}>
                        {new Date(date).toLocaleString('sv-SE')}
                      </li>
                    ))}
                  </ul>
                </div>
              ) : (
                <p style={{
                  fontSize: '0.875rem',
                  color: '#991b1b',
                  margin: 0
                }}>
                  ❌ {cronInfo.error}
                </p>
              )}
            </div>
          )}
        </div>

        <div className="form-group">
          <label className="form-label">Tidszon</label>
          <select
            value={formData.timezone}
            onChange={(e) => setFormData({ ...formData, timezone: e.target.value })}
            className="form-input"
          >
            {timezones.map(tz => (
              <option key={tz} value={tz}>{tz}</option>
            ))}
          </select>
        </div>

        <div className="form-group">
          <label className="form-label">Status</label>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.75rem',
            padding: '0.75rem',
            backgroundColor: '#fbf9f8',
            borderRadius: '0.5rem',
            border: '1px solid #e5e7eb'
          }}>
            <input
              type="checkbox"
              id="enabled"
              checked={formData.enabled}
              onChange={(e) => setFormData({ ...formData, enabled: e.target.checked })}
              style={{
                width: '1rem',
                height: '1rem',
                accentColor: '#fd746c'
              }}
            />
            <label htmlFor="enabled" style={{
              fontSize: '0.875rem',
              fontWeight: '500',
              color: '#1a0f0f',
              cursor: 'pointer'
            }}>
              {formData.enabled ? '✅ Schema aktiverat' : '⏸️ Schema inaktiverat'}
            </label>
          </div>
        </div>

        {/* Action Buttons */}
        <div style={{
          borderTop: '1px solid #e5e7eb',
          padding: '1.5rem 0 0 0',
          marginTop: '1.5rem',
          display: 'flex',
          justifyContent: 'flex-end',
          gap: '1rem'
        }}>
          <button
            type="button"
            onClick={onCancel}
            className="action-button secondary"
          >
            <span>Avbryt</span>
          </button>

          <button
            type="submit"
            disabled={loading || !cronInfo?.isValid}
            className="action-button primary"
            style={{
              opacity: (loading || !cronInfo?.isValid) ? 0.5 : 1,
              cursor: (loading || !cronInfo?.isValid) ? 'not-allowed' : 'pointer'
            }}
          >
            {loading ? (
              <span>Sparar...</span>
            ) : (
              <span>{schedule ? 'Uppdatera schema' : 'Skapa schema'}</span>
            )}
          </button>
        </div>

        {(!cronInfo?.isValid && cronInfo) && (
          <div style={{
            padding: '0.75rem',
            backgroundColor: '#fef3c7',
            borderRadius: '0.5rem',
            border: '1px solid #fed7aa',
            marginTop: '1rem'
          }}>
            <p style={{
              fontSize: '0.875rem',
              color: '#92400e',
              margin: 0
            }}>
              ⚠️ Vänligen fixa cron-uttrycket innan du sparar
            </p>
          </div>
        )}
      </form>
    </div>
  )
}

export default ScheduleForm
