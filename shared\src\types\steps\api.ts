import { RpaStepBase } from './base';

// API steps (future implementation)
// These are placeholder types for future API integration functionality

export interface ApiCallStep extends RpaStepBase {
  type: 'apiCall';
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  variableName?: string; // Variable name to store response
}

export interface ApiAuthStep extends RpaStepBase {
  type: 'apiAuth';
  authType: 'bearer' | 'basic' | 'apikey';
  credentialId: string;
  variableName?: string; // Variable name to store auth token
}

// Fortnox API steps

/**
 * Fortnox Create Voucher step
 * Uses AI to create voucher rows based on input data and user prompt
 */
export interface FortnoxCreateVoucherStep extends RpaStepBase {
  type: 'fortnoxCreateVoucher';
  description?: string; // Voucher description
  inputVariable: string; // Variable containing input data for AI processing
  aiPrompt: string; // AI prompt describing how to create the voucher
  voucherSeries?: string; // Optional voucher series (defaults to 'A')
  transactionDate?: string; // Optional transaction date (defaults to today)
  variableName?: string; // Variable name to store voucher result (defaults to 'var-fortnox-voucher')
  fileIds?: string[]; // Optional array of file IDs to attach to the voucher
}

/**
 * Fortnox Upload File step
 * Uploads a file to Fortnox Archive and returns file ID for later use
 */
export interface FortnoxUploadFileStep extends RpaStepBase {
  type: 'fortnoxUploadFile';
  inputVariable: string; // Variable containing base64 file content
  filename?: string; // Optional custom filename (if not provided, uses {inputVariable}_filename)
  description?: string; // Optional file description
  variableName?: string; // Variable name to store file result (defaults to 'var-fortnox-file')
}

/**
 * Fortnox Attach File to Voucher step
 * Attaches an already uploaded file to a specific voucher
 */
export interface FortnoxAttachFileToVoucherStep extends RpaStepBase {
  type: 'fortnoxAttachFileToVoucher';
  fileIdVariable: string; // Variable containing the file ID from upload
  voucherNumberVariable: string; // Variable containing voucher number
  voucherSeriesVariable?: string; // Variable containing voucher series (defaults to 'A')
  variableName?: string; // Variable name to store attachment result (defaults to 'var-fortnox-attachment')
}

/**
 * Fortnox Upload and Create Voucher step
 * Combined step that uploads file and creates voucher with automatic attachment
 */
export interface FortnoxUploadAndCreateVoucherStep extends RpaStepBase {
  type: 'fortnoxUploadAndCreateVoucher';
  // File upload properties
  fileInputVariable: string; // Variable containing base64 file content
  filename?: string; // Optional custom filename
  fileDescription?: string; // Optional file description
  // Voucher creation properties
  voucherInputVariable: string; // Variable containing input data for AI processing
  aiPrompt: string; // AI prompt describing how to create the voucher
  voucherDescription?: string; // Voucher description
  voucherSeries?: string; // Optional voucher series (defaults to 'A')
  transactionDate?: string; // Optional transaction date (defaults to today)
  variableName?: string; // Variable name to store combined result (defaults to 'var-fortnox-voucher-with-file')
}

// eAccounting API steps

/**
 * eAccounting Upload File step
 * Uploads a file to eAccounting Attachments and returns attachment ID for later use
 */
export interface EAccountingUploadFileStep extends RpaStepBase {
  type: 'eAccountingUploadFile';
  inputVariable: string; // Variable containing base64 file content
  filename?: string; // Optional custom filename (if not provided, uses {inputVariable}_filename)
  contentType?: string; // Optional content type (defaults to auto-detect from filename)
  comment?: string; // Optional file comment/description
  variableName?: string; // Variable name to store file result (defaults to 'var-eaccounting-file')
}

/**
 * eAccounting Create Voucher step
 * Uses AI to create voucher rows based on input data and user prompt
 */
export interface EAccountingCreateVoucherStep extends RpaStepBase {
  type: 'eAccountingCreateVoucher';
  voucherText?: string; // Voucher description/text
  inputVariable: string; // Variable containing input data for AI processing
  aiPrompt: string; // AI prompt describing how to create the voucher
  voucherDate?: string; // Optional voucher date (defaults to today, format: yyyy-mm-dd)
  numberSeries?: string; // Optional voucher number series
  variableName?: string; // Variable name to store voucher result (defaults to 'var-eaccounting-voucher')
}

/**
 * eAccounting Attach File to Voucher step
 * Attaches an already uploaded file to a specific voucher
 */
export interface EAccountingAttachFileToVoucherStep extends RpaStepBase {
  type: 'eAccountingAttachFileToVoucher';
  attachmentIdVariable: string; // Variable containing the attachment ID from upload
  voucherIdVariable: string; // Variable containing voucher ID
  variableName?: string; // Variable name to store attachment result (defaults to 'var-eaccounting-attachment')
}

/**
 * eAccounting Upload and Create Voucher step
 * Combined step that uploads file and creates voucher with automatic attachment
 */
export interface EAccountingUploadAndCreateVoucherStep extends RpaStepBase {
  type: 'eAccountingUploadAndCreateVoucher';
  // File upload properties
  fileInputVariable: string; // Variable containing base64 file content
  filename?: string; // Optional custom filename
  contentType?: string; // Optional content type
  fileComment?: string; // Optional file comment/description
  // Voucher creation properties
  voucherInputVariable: string; // Variable containing input data for AI processing
  aiPrompt: string; // AI prompt describing how to create the voucher
  voucherText?: string; // Voucher description/text
  voucherDate?: string; // Optional voucher date (defaults to today, format: yyyy-mm-dd)
  numberSeries?: string; // Optional voucher number series
  variableName?: string; // Variable name to store combined result (defaults to 'var-eaccounting-voucher-with-file')
}
