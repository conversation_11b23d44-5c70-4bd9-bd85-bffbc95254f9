// Template för step-executors för ny runner
// <PERSON><PERSON>pa som backend/src/runners/{{KEBAB_CASE}}/stepExecutors/index.ts

import { ExecutionContext } from '../../types';
// Importera step-typer som denna runner hanterar
// import { {{PASCAL_CASE}}Step } from '../../../../shared/src/types/steps/{{STEP_CATEGORY}}';

/**
 * Step Executors för {{PASCAL_CASE}}Runner
 * 
 * Denna fil innehåller alla step-execution metoder för {{PASCAL_CASE}}Runner.
 * Varje metod hanterar en specifik typ av RPA-steg.
 */

/**
 * Bas-klass för step executors
 */
export abstract class BaseStepExecutor {
  protected logInfo(message: string): void {
    console.log(`[${new Date().toISOString()}] INFO: ${message}`);
  }

  protected logError(message: string): void {
    console.error(`[${new Date().toISOString()}] ERROR: ${message}`);
  }

  protected logWarning(message: string): void {
    console.warn(`[${new Date().toISOString()}] WARNING: ${message}`);
  }
}

/**
 * Executor för {{STEP_TYPE}} steg
 */
export class {{PASCAL_CASE}}StepExecutor extends BaseStepExecutor {
  // Lägg till executor-specifika properties
  // private client: SomeClient;

  constructor(/* lägg till dependencies */) {
    super();
    // Initialisera dependencies
  }

  /**
   * Utför {{STEP_TYPE}} steg
   */
  async execute(
    step: any, // Ersätt med korrekt step-typ
    context: ExecutionContext
  ): Promise<void> {
    try {
      this.logInfo(`Utför {{STEP_TYPE}}: ${step.name}`);

      // Validera step-data
      this.validateStep(step);

      // Utför step-logik
      await this.performStepAction(step, context);

      // Uppdatera context om nödvändigt
      this.updateContext(step, context);

      this.logInfo(`{{STEP_TYPE}} slutfört framgångsrikt`);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Okänt fel';
      this.logError(`Fel vid {{STEP_TYPE}}: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Validerar step-data
   */
  private validateStep(step: any): void {
    // Implementera step-specifik validering
    // Exempel:
    // if (!step.requiredField) {
    //   throw new Error('Obligatoriskt fält saknas');
    // }
  }

  /**
   * Utför huvudåtgärden för steget
   */
  private async performStepAction(
    step: any, 
    context: ExecutionContext
  ): Promise<void> {
    // Implementera huvudlogiken för steget
    // Exempel för API-call:
    // const response = await this.client.makeRequest({
    //   url: step.url,
    //   method: step.method,
    //   data: step.data
    // });
    // return response;

    // Exempel för AI-processing:
    // const result = await this.aiClient.process({
    //   prompt: step.prompt,
    //   model: step.model,
    //   input: step.input
    // });
    // return result;

    // Exempel för file-processing:
    // const processedData = await this.fileProcessor.process({
    //   filePath: step.filePath,
    //   operation: step.operation
    // });
    // return processedData;
  }

  /**
   * Uppdaterar execution context med resultat
   */
  private updateContext(step: any, context: ExecutionContext): void {
    // Lägg till variabler i context baserat på step-resultat
    // Exempel:
    // context.variables[`var-${step.type}-result`] = result;
    // context.variables[`var-${step.type}-timestamp`] = new Date().toISOString();
  }
}

// Lägg till fler executors för andra steg-typer som runner hanterar
// export class AnotherStepExecutor extends BaseStepExecutor {
//   async execute(step: AnotherStep, context: ExecutionContext): Promise<void> {
//     // Implementera logik för annat steg
//   }
// }

/**
 * Factory för att skapa step executors
 */
export class StepExecutorFactory {
  // Lägg till executor instances
  private {{STEP_TYPE}}Executor: {{PASCAL_CASE}}StepExecutor;

  constructor(/* dependencies */) {
    // Initialisera executors
    this.{{STEP_TYPE}}Executor = new {{PASCAL_CASE}}StepExecutor(/* dependencies */);
  }

  /**
   * Hämtar lämplig executor för en step-typ
   */
  getExecutor(stepType: string): BaseStepExecutor {
    switch (stepType) {
      case '{{STEP_TYPE}}':
        return this.{{STEP_TYPE}}Executor;
      
      // Lägg till cases för andra steg-typer
      // case 'anotherStepType':
      //   return this.anotherStepExecutor;

      default:
        throw new Error(`Ingen executor hittad för step-typ: ${stepType}`);
    }
  }

  /**
   * Kontrollerar om en step-typ stöds
   */
  supportsStepType(stepType: string): boolean {
    const supportedTypes = [
      '{{STEP_TYPE}}',
      // Lägg till andra stödda typer
    ];
    
    return supportedTypes.includes(stepType);
  }
}
