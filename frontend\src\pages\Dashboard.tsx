import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { flowApi, executionApi, scheduleApi } from '../services/api'

interface DashboardStats {
  totalFlows: number
  activeSchedules: number
  completedExecutions: number
  totalFlowsChange: string
  activeSchedulesChange: string
  completedExecutionsChange: string
}

interface RecentActivity {
  flowName: string
  status: 'Slutförd' | 'Körs' | 'Schemalagd' | 'Misslyckad'
  lastRun: string
  nextRun: string
}

export function Dashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalFlows: 0,
    activeSchedules: 0,
    completedExecutions: 0,
    totalFlowsChange: '+0%',
    activeSchedulesChange: '+0%',
    completedExecutionsChange: '+0%'
  })
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)

      // Hämta flows
      const flowsResponse = await flowApi.getFlows()
      const flows = flowsResponse.data || []

      // Hämta executions
      const executionsResponse = await executionApi.getExecutions({ limit: 100 })
      const executions = executionsResponse.data || []

      // Hämta schedules
      const schedulesResponse = await scheduleApi.getSchedules({ enabled: true })
      const schedules = schedulesResponse.data || []

      // Beräkna statistik
      const totalFlows = flows.length
      const completedExecutions = executions.filter(e => e.status === 'completed').length
      const activeSchedules = schedules.length

      setStats({
        totalFlows,
        activeSchedules,
        completedExecutions,
        totalFlowsChange: '+10%',
        activeSchedulesChange: '+5%',
        completedExecutionsChange: '+20%'
      })
      
      // Skapa recent activity från flows och executions
      const activity: RecentActivity[] = flows.slice(0, 5).map((flow) => {
        const flowExecutions = executions.filter(e => e.flowId === flow.id)
        const lastExecution = flowExecutions.sort((a, b) =>
          new Date(b.startedAt).getTime() - new Date(a.startedAt).getTime()
        )[0]

        // Hitta schedule för detta flow
        const flowSchedule = schedules.find(s => s.flowId === flow.id)

        // Bestäm status baserat på senaste execution och schedule
        let status: 'Slutförd' | 'Körs' | 'Schemalagd' | 'Misslyckad' = 'Schemalagd'
        if (lastExecution) {
          switch (lastExecution.status) {
            case 'completed':
              status = 'Slutförd'
              break
            case 'running':
              status = 'Körs'
              break
            case 'failed':
              status = 'Misslyckad'
              break
            default:
              status = 'Schemalagd'
          }
        }

        return {
          flowName: flow.name,
          status,
          lastRun: lastExecution ?
            new Date(lastExecution.startedAt).toLocaleString('sv-SE', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit'
            }) : 'Aldrig',
          nextRun: flowSchedule && flowSchedule.enabled ? 'Schemalagd' : 'N/A'
        }
      })
      
      setRecentActivity(activity)
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusButtonClass = (status: string) => {
    const baseClass = "status-button"

    switch (status) {
      case 'Slutförd':
        return `${baseClass} status-completed`
      case 'Körs':
        return `${baseClass} status-running`
      case 'Schemalagd':
        return `${baseClass} status-scheduled`
      case 'Misslyckad':
        return `${baseClass} status-failed`
      default:
        return `${baseClass} status-scheduled`
    }
  }

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-text">Laddar dashboard...</div>
      </div>
    )
  }

  return (
    <div className="dashboard-container">
      {/* Header */}
      <div className="dashboard-header">
        <div className="dashboard-header-content">
          <p className="dashboard-title">Översikt</p>
          <p className="dashboard-subtitle">
            Översikt över dina automatiseringsaktiviteter och prestanda.
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="stats-grid">
        <div className="stat-card">
          <p className="stat-label">Totalt antal flöden</p>
          <p className="stat-value">{stats.totalFlows}</p>
          <p className="stat-change">{stats.totalFlowsChange}</p>
        </div>
        <div className="stat-card">
          <p className="stat-label">Aktiva scheman</p>
          <p className="stat-value">{stats.activeSchedules}</p>
          <p className="stat-change">{stats.activeSchedulesChange}</p>
        </div>
        <div className="stat-card">
          <p className="stat-label">Slutförda körningar</p>
          <p className="stat-value">{stats.completedExecutions}</p>
          <p className="stat-change">{stats.completedExecutionsChange}</p>
        </div>
      </div>

      {/* Recent Activity */}
      <h2 className="section-title">
        Senaste aktivitet
      </h2>
      <div className="table-container">
        <div className="activity-table">
          <table className="table">
            <thead>
              <tr>
                <th>Flödesnamn</th>
                <th>Status</th>
                <th>Senaste körning</th>
                <th>Nästa körning</th>
              </tr>
            </thead>
            <tbody>
              {recentActivity.map((activity, index) => (
                <tr key={index}>
                  <td>{activity.flowName}</td>
                  <td>
                    <button className={getStatusButtonClass(activity.status)}>
                      <span>{activity.status}</span>
                    </button>
                  </td>
                  <td className="secondary-text">{activity.lastRun}</td>
                  <td className="secondary-text">{activity.nextRun}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Quick Actions */}
      <h2 className="section-title">
        Snabbåtgärder
      </h2>
      <div className="quick-actions">
        <div className="quick-actions-content">
          <Link to="/flows/new" className="action-button primary">
            <span>Skapa nytt flöde</span>
          </Link>
          <Link to="/schedules" className="action-button secondary">
            <span>Schemalägg ny körning</span>
          </Link>
        </div>
      </div>
    </div>
  )
}
