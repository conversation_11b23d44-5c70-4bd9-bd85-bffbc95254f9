# Exempel: Skapa Custom Step

Detta exempel visar hur man skapar ett nytt RPA-steg fr<PERSON>n grunden med hjälp av templates och verktyg.

## Scenario

Vi vill skapa ett nytt steg som kan extrahera data från en tabell på en webbsida och spara resultatet som JSON.

**Steg-namn**: `extractTableData`
**Kategori**: `extraction`
**Runner**: `playwright`

## Steg 1: Generera med Script

```bash
npm run generate:step -- --type=extractTableData --category=extraction --runner=playwright
```

Detta skapar alla grundläggande filer:

- `shared/src/types/steps/extraction.ts` - Step-typ definition
- `shared/src/validators/steps/extraction.ts` - Validator
- `backend/src/runners/playwright/PlaywrightRunner.ts` - Runner-metod
- `frontend/src/components/flow-editor/step-editors/extraction/ExtractTableDataStepEditor.tsx` - Editor
- `frontend/src/components/flow-editor/step-definitions/extraction.ts` - Toolbar-definition

## Steg 2: Anpassa Step-typ

Redigera `shared/src/types/steps/extraction.ts`:

```typescript
export interface ExtractTableDataStep extends RpaStepBase {
  type: 'extractTableData';
  tableSelector: string;
  includeHeaders?: boolean;
  maxRows?: number;
  outputFormat?: 'json' | 'csv';
  variableName?: string;
}
```

## Steg 3: Implementera Validator

Uppdatera `shared/src/validators/steps/extraction.ts`:

```typescript
export function validateExtractTableDataStep(step: ExtractTableDataStep): string[] {
  const errors: string[] = [];

  if (!step.tableSelector?.trim()) {
    errors.push('Tabell-selector är obligatorisk');
  }

  if (step.maxRows !== undefined && (step.maxRows < 1 || step.maxRows > 10000)) {
    errors.push('Max rader måste vara mellan 1 och 10000');
  }

  if (step.outputFormat && !['json', 'csv'].includes(step.outputFormat)) {
    errors.push('Output-format måste vara json eller csv');
  }

  return errors;
}

export function createExtractTableDataStep(): ExtractTableDataStep {
  return {
    id: '',
    type: 'extractTableData',
    name: 'Extrahera Tabelldata',
    description: '',
    tableSelector: 'table',
    includeHeaders: true,
    maxRows: 100,
    outputFormat: 'json',
    variableName: 'tableData',
  };
}
```

## Steg 4: Implementera Runner-logik

Uppdatera `backend/src/runners/playwright/PlaywrightRunner.ts`:

```typescript
async executeExtractTableDataStep(
  step: ExtractTableDataStep, 
  context: ExecutionContext
): Promise<void> {
  try {
    this.logInfo(`Extraherar tabelldata: ${step.name}`);

    if (!this.page) {
      throw new Error('Browser page är inte tillgänglig');
    }

    // Vänta på tabell
    await this.page.waitForSelector(step.tableSelector, { timeout: 10000 });

    // Extrahera tabelldata
    const tableData = await this.page.evaluate((selector, includeHeaders, maxRows) => {
      const table = document.querySelector(selector);
      if (!table) return null;

      const rows = Array.from(table.querySelectorAll('tr'));
      const data: string[][] = [];

      let startIndex = includeHeaders ? 0 : 1;
      let endIndex = maxRows ? Math.min(rows.length, startIndex + maxRows) : rows.length;

      for (let i = startIndex; i < endIndex; i++) {
        const cells = Array.from(rows[i].querySelectorAll('td, th'));
        const rowData = cells.map(cell => cell.textContent?.trim() || '');
        data.push(rowData);
      }

      return data;
    }, step.tableSelector, step.includeHeaders, step.maxRows);

    if (!tableData) {
      throw new Error('Kunde inte hitta tabell med selector: ' + step.tableSelector);
    }

    // Formatera data
    let formattedData: any;
    if (step.outputFormat === 'csv') {
      formattedData = tableData.map(row => row.join(',')).join('\n');
    } else {
      // JSON format
      if (step.includeHeaders && tableData.length > 0) {
        const headers = tableData[0];
        const rows = tableData.slice(1);
        formattedData = rows.map(row => {
          const obj: Record<string, string> = {};
          headers.forEach((header, index) => {
            obj[header] = row[index] || '';
          });
          return obj;
        });
      } else {
        formattedData = tableData;
      }
    }

    // Spara i context
    const variableName = step.variableName || 'var-extract-table-data';
    context.variables[variableName] = formattedData;
    context.variables[`${variableName}-count`] = tableData.length;

    await this.randomDelay();

    this.logInfo(`Extraherade ${tableData.length} rader från tabell`);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Okänt fel';
    this.logError(`Fel vid tabellextraktion: ${errorMessage}`);
    throw error;
  }
}
```

## Steg 5: Skapa Editor-komponent

Uppdatera `frontend/src/components/flow-editor/step-editors/extraction/ExtractTableDataStepEditor.tsx`:

```typescript
import React from 'react';
import { ExtractTableDataStep } from '../../../../../shared/src/types/steps/extraction';
import { BaseStepEditorProps } from '../base/BaseStepEditor';

interface ExtractTableDataStepEditorProps extends BaseStepEditorProps {
  step: ExtractTableDataStep;
}

export const ExtractTableDataStepEditor: React.FC<ExtractTableDataStepEditorProps> = ({
  step,
  onStepChange,
  variables = []
}) => {
  const handleChange = (field: keyof ExtractTableDataStep, value: any) => {
    onStepChange({
      ...step,
      [field]: value
    });
  };

  return (
    <div className="step-editor">
      <div className="form-row">
        <div className="form-group">
          <label>Namn</label>
          <input
            type="text"
            value={step.name || ''}
            onChange={(e) => handleChange('name', e.target.value)}
            className="form-input"
          />
        </div>
      </div>

      <div className="form-row">
        <div className="form-group">
          <label>Tabell Selector</label>
          <input
            type="text"
            value={step.tableSelector || ''}
            onChange={(e) => handleChange('tableSelector', e.target.value)}
            placeholder="table, .data-table, #myTable"
            className="form-input"
          />
        </div>
      </div>

      <div className="form-row">
        <div className="form-group">
          <label>Variabelnamn</label>
          <input
            type="text"
            value={step.variableName || ''}
            onChange={(e) => handleChange('variableName', e.target.value)}
            placeholder="tableData"
            className="form-input"
          />
        </div>
        <div className="form-group">
          <label>Max Rader</label>
          <input
            type="number"
            value={step.maxRows || 100}
            onChange={(e) => handleChange('maxRows', parseInt(e.target.value))}
            min="1"
            max="10000"
            className="form-input"
          />
        </div>
      </div>

      <div className="form-row">
        <div className="form-group">
          <label>Output Format</label>
          <select
            value={step.outputFormat || 'json'}
            onChange={(e) => handleChange('outputFormat', e.target.value)}
            className="form-select"
          >
            <option value="json">JSON</option>
            <option value="csv">CSV</option>
          </select>
        </div>
        <div className="form-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={step.includeHeaders || false}
              onChange={(e) => handleChange('includeHeaders', e.target.checked)}
            />
            Inkludera headers
          </label>
        </div>
      </div>
    </div>
  );
};
```

## Steg 6: Uppdatera Toolbar

Uppdatera `frontend/src/components/flow-editor/step-definitions/extraction.ts`:

```typescript
export const extractTableDataDefinition: StepDefinition = {
  type: 'extractTableData',
  name: 'Extrahera Tabelldata',
  description: 'Extraherar data från en HTML-tabell',
  category: 'extraction',
  icon: 'Table',
  color: '#fd746c',
  
  defaultStep: {
    id: '',
    type: 'extractTableData',
    name: 'Extrahera Tabelldata',
    description: '',
    tableSelector: 'table',
    includeHeaders: true,
    maxRows: 100,
    outputFormat: 'json',
    variableName: 'tableData',
  },
  
  isValid: (step: any) => {
    return !!(step.name?.trim() && step.tableSelector?.trim());
  },
  
  tooltip: 'Extraherar data från HTML-tabell och sparar som JSON eller CSV',
  requiredRunner: 'playwright',
  createsVariables: ['tableData', 'tableData-count'],
  requiresVariables: [],
  
  examples: [
    {
      name: 'Extrahera produkttabell',
      description: 'Extraherar produktdata från e-handelssida',
      step: {
        id: 'example-1',
        type: 'extractTableData',
        name: 'Extrahera Produkter',
        tableSelector: '.product-table',
        includeHeaders: true,
        maxRows: 50,
        outputFormat: 'json',
        variableName: 'products',
      }
    }
  ]
};
```

## Steg 7: Registrera Komponenter

Uppdatera registries manuellt enligt instruktioner från generate-scriptet.

## Steg 8: Testa

1. Bygg projektet:
```bash
npm run build
```

2. Starta utvecklingsservern:
```bash
npm run dev
```

3. Skapa nytt flöde och testa det nya steget

## Resultat

Nu har du ett fullt fungerande `extractTableData` steg som kan:

- Extrahera data från HTML-tabeller
- Hantera headers
- Begränsa antal rader
- Exportera som JSON eller CSV
- Spara data i variabler för användning i andra steg

## Nästa Steg

- Lägg till felhantering för edge cases
- Implementera enhetstester
- Dokumentera steget i API-dokumentationen
- Skapa fler exempel för olika användningsfall
