// Core interfaces and base classes
export * from './base';

// Registry and factory
export * from './registry';
export * from './factory';

// Specific runner implementations
export * from './playwright';
export * from './ai';
export * from './api';

// Flow executor
export { FlowExecutor } from './FlowExecutor';

/**
 * Initialize the runner system with default configurations
 * This should be called during application startup
 */
export function initializeRunnerSystem(): void {
  const { getRunnerFactory } = require('./factory');
  const factory = getRunnerFactory();
  console.log('🔧 Runner system initialized');
  console.log('📊 Factory stats:', factory.getStats());
}
