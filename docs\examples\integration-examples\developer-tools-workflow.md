# Integration Exempel: Komplett Utvecklarverktyg-workflow

Detta exempel visar hur man använder alla utvecklarverktyg tillsammans för att skapa ett komplett nytt steg från början till slut.

## Scenario

Vi ska skapa ett nytt steg som kan läsa QR-koder från bilder och extrahera informationen.

**Steg-namn**: `readQRCode`
**Kategori**: `extraction`
**Runner**: `ai` (använder AI för bildanalys)

## Steg 1: Planering

Innan vi bö<PERSON>, planerar vi vad steget ska göra:

1. Ta en bild (från fil eller screenshot)
2. Analysera bilden för QR-koder
3. Extrahera text från QR-koden
4. Spara resultatet i variabler

## Steg 2: Generera med Templates

```bash
# Använd step-generator
npm run generate:step -- --type=readQRCode --category=extraction --runner=ai
```

Detta skapar grundstrukturen för vårt steg.

## Steg 3: Följ Utvecklardokumentation

Läs igenom relevanta guider:

```bash
# Öppna dokumentation
docs/development/adding-new-steps.md
docs/development/architecture.md
docs/development/conventions.md
```

## Steg 4: Implementera Step-typ

Redigera `shared/src/types/steps/extraction.ts`:

```typescript
export interface ReadQRCodeStep extends RpaStepBase {
  type: 'readQRCode';
  imageSource: 'file' | 'screenshot' | 'variable';
  imagePath?: string;
  imageVariable?: string;
  outputVariable?: string;
  multipleQRCodes?: boolean;
  timeout?: number;
}
```

## Steg 5: Implementera Validator

Uppdatera `shared/src/validators/steps/extraction.ts`:

```typescript
export function validateReadQRCodeStep(step: ReadQRCodeStep): string[] {
  const errors: string[] = [];

  if (!step.imageSource) {
    errors.push('Bildkälla är obligatorisk');
  }

  if (step.imageSource === 'file' && !step.imagePath?.trim()) {
    errors.push('Bildsökväg är obligatorisk när bildkälla är fil');
  }

  if (step.imageSource === 'variable' && !step.imageVariable?.trim()) {
    errors.push('Bildvariabel är obligatorisk när bildkälla är variabel');
  }

  if (step.timeout !== undefined && (step.timeout < 1000 || step.timeout > 60000)) {
    errors.push('Timeout måste vara mellan 1000 och 60000ms');
  }

  return errors;
}

export function createReadQRCodeStep(): ReadQRCodeStep {
  return {
    id: '',
    type: 'readQRCode',
    name: 'Läs QR-kod',
    description: '',
    imageSource: 'screenshot',
    outputVariable: 'qrCodeData',
    multipleQRCodes: false,
    timeout: 10000,
  };
}
```

## Steg 6: Implementera AI Runner-logik

Uppdatera `backend/src/runners/ai/AIRunner.ts`:

```typescript
async executeReadQRCodeStep(
  step: ReadQRCodeStep, 
  context: ExecutionContext
): Promise<void> {
  try {
    this.logInfo(`Läser QR-kod: ${step.name}`);

    let imageData: string;

    // Hämta bilddata baserat på källa
    switch (step.imageSource) {
      case 'file':
        if (!step.imagePath) {
          throw new Error('Bildsökväg saknas');
        }
        imageData = await this.readImageFile(step.imagePath);
        break;
        
      case 'screenshot':
        // Använd senaste screenshot från context
        imageData = context.variables['var-screenshot'] || '';
        if (!imageData) {
          throw new Error('Ingen screenshot tillgänglig. Kör takeScreenshot först.');
        }
        break;
        
      case 'variable':
        if (!step.imageVariable) {
          throw new Error('Bildvariabel saknas');
        }
        imageData = context.variables[step.imageVariable] || '';
        if (!imageData) {
          throw new Error(`Variabel ${step.imageVariable} innehåller ingen bilddata`);
        }
        break;
        
      default:
        throw new Error(`Okänd bildkälla: ${step.imageSource}`);
    }

    // Analysera bild med OpenAI Vision API
    const qrCodeData = await this.analyzeImageForQRCode(imageData, step.multipleQRCodes);

    // Spara resultat i context
    const outputVar = step.outputVariable || 'var-qr-code-data';
    context.variables[outputVar] = qrCodeData;
    context.variables[`${outputVar}-count`] = Array.isArray(qrCodeData) ? qrCodeData.length : 1;

    this.logInfo(`QR-kod läst framgångsrikt: ${JSON.stringify(qrCodeData)}`);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Okänt fel';
    this.logError(`Fel vid QR-kod läsning: ${errorMessage}`);
    throw error;
  }
}

private async readImageFile(filePath: string): Promise<string> {
  const fs = require('fs');
  const path = require('path');
  
  if (!fs.existsSync(filePath)) {
    throw new Error(`Bildfil inte hittad: ${filePath}`);
  }
  
  const imageBuffer = fs.readFileSync(filePath);
  return imageBuffer.toString('base64');
}

private async analyzeImageForQRCode(imageData: string, multiple: boolean = false): Promise<string | string[]> {
  const prompt = multiple 
    ? "Analysera denna bild och hitta alla QR-koder. Returnera texten från varje QR-kod som en JSON-array."
    : "Analysera denna bild och hitta QR-koden. Returnera endast texten från QR-koden.";

  const response = await this.openaiClient.chat.completions.create({
    model: 'gpt-4o',
    messages: [
      {
        role: 'user',
        content: [
          { type: 'text', text: prompt },
          {
            type: 'image_url',
            image_url: {
              url: `data:image/jpeg;base64,${imageData}`
            }
          }
        ]
      }
    ],
    max_tokens: 1000
  });

  const result = response.choices[0]?.message?.content || '';
  
  if (multiple) {
    try {
      return JSON.parse(result);
    } catch {
      // Om JSON parsing misslyckas, returnera som array med ett element
      return [result];
    }
  }
  
  return result;
}
```

## Steg 7: Skapa Editor-komponent

Skapa `frontend/src/components/flow-editor/step-editors/extraction/ReadQRCodeStepEditor.tsx`:

```typescript
import React from 'react';
import { ReadQRCodeStep } from '../../../../../shared/src/types/steps/extraction';
import { BaseStepEditorProps } from '../base/BaseStepEditor';

interface ReadQRCodeStepEditorProps extends BaseStepEditorProps {
  step: ReadQRCodeStep;
}

export const ReadQRCodeStepEditor: React.FC<ReadQRCodeStepEditorProps> = ({
  step,
  onStepChange,
  variables = []
}) => {
  const handleChange = (field: keyof ReadQRCodeStep, value: any) => {
    onStepChange({
      ...step,
      [field]: value
    });
  };

  const imageVariables = variables.filter(v => 
    v.includes('screenshot') || v.includes('image') || v.includes('file')
  );

  return (
    <div className="step-editor">
      <div className="form-row">
        <div className="form-group">
          <label>Namn</label>
          <input
            type="text"
            value={step.name || ''}
            onChange={(e) => handleChange('name', e.target.value)}
            className="form-input"
          />
        </div>
      </div>

      <div className="form-row">
        <div className="form-group">
          <label>Bildkälla</label>
          <select
            value={step.imageSource || 'screenshot'}
            onChange={(e) => handleChange('imageSource', e.target.value)}
            className="form-select"
          >
            <option value="screenshot">Senaste Screenshot</option>
            <option value="file">Fil på disk</option>
            <option value="variable">Från variabel</option>
          </select>
        </div>
      </div>

      {step.imageSource === 'file' && (
        <div className="form-row">
          <div className="form-group">
            <label>Bildsökväg</label>
            <input
              type="text"
              value={step.imagePath || ''}
              onChange={(e) => handleChange('imagePath', e.target.value)}
              placeholder="/path/to/image.jpg"
              className="form-input"
            />
          </div>
        </div>
      )}

      {step.imageSource === 'variable' && (
        <div className="form-row">
          <div className="form-group">
            <label>Bildvariabel</label>
            <select
              value={step.imageVariable || ''}
              onChange={(e) => handleChange('imageVariable', e.target.value)}
              className="form-select"
            >
              <option value="">Välj variabel...</option>
              {imageVariables.map(variable => (
                <option key={variable} value={variable}>
                  {variable}
                </option>
              ))}
            </select>
          </div>
        </div>
      )}

      <div className="form-row">
        <div className="form-group">
          <label>Output-variabel</label>
          <input
            type="text"
            value={step.outputVariable || ''}
            onChange={(e) => handleChange('outputVariable', e.target.value)}
            placeholder="qrCodeData"
            className="form-input"
          />
        </div>
        <div className="form-group">
          <label>Timeout (ms)</label>
          <input
            type="number"
            value={step.timeout || 10000}
            onChange={(e) => handleChange('timeout', parseInt(e.target.value))}
            min="1000"
            max="60000"
            className="form-input"
          />
        </div>
      </div>

      <div className="form-row">
        <div className="form-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={step.multipleQRCodes || false}
              onChange={(e) => handleChange('multipleQRCodes', e.target.checked)}
            />
            Hitta flera QR-koder
          </label>
        </div>
      </div>
    </div>
  );
};
```

## Steg 8: Uppdatera Toolbar

Uppdatera `frontend/src/components/flow-editor/step-definitions/extraction.ts`:

```typescript
export const readQRCodeDefinition: StepDefinition = {
  type: 'readQRCode',
  name: 'Läs QR-kod',
  description: 'Läser QR-koder från bilder med AI',
  category: 'extraction',
  icon: 'QrCode',
  color: '#fd746c',
  
  defaultStep: {
    id: '',
    type: 'readQRCode',
    name: 'Läs QR-kod',
    description: '',
    imageSource: 'screenshot',
    outputVariable: 'qrCodeData',
    multipleQRCodes: false,
    timeout: 10000,
  },
  
  isValid: (step: any) => {
    if (!step.name?.trim()) return false;
    if (step.imageSource === 'file' && !step.imagePath?.trim()) return false;
    if (step.imageSource === 'variable' && !step.imageVariable?.trim()) return false;
    return true;
  },
  
  tooltip: 'Använder AI för att läsa QR-koder från bilder',
  requiredRunner: 'ai',
  createsVariables: ['qrCodeData', 'qrCodeData-count'],
  requiresVariables: [], // Dynamiskt baserat på imageSource
  
  examples: [
    {
      name: 'Läs QR från screenshot',
      description: 'Läser QR-kod från senaste screenshot',
      step: {
        id: 'example-1',
        type: 'readQRCode',
        name: 'Läs QR från skärm',
        imageSource: 'screenshot',
        outputVariable: 'qrData',
        multipleQRCodes: false,
      }
    }
  ]
};
```

## Steg 9: Registrera Komponenter

Följ instruktionerna från generate-scriptet för att uppdatera:

1. `shared/src/types/steps/index.ts` - Lägg till i RpaStep union
2. `shared/src/validators/steps/index.ts` - Lägg till validator case
3. `backend/src/runners/registry/stepTypes.ts` - Lägg till mappning
4. `frontend/src/components/flow-editor/step-editors/StepEditorRegistry.tsx` - Registrera editor
5. `frontend/src/components/flow-editor/step-definitions/index.ts` - Lägg till definition

## Steg 10: Skriv Tester

Skapa `shared/src/validators/steps/__tests__/extraction.test.ts`:

```typescript
import { validateReadQRCodeStep, createReadQRCodeStep } from '../extraction';

describe('ReadQRCode Step Validation', () => {
  test('ska validera giltigt steg', () => {
    const step = createReadQRCodeStep();
    const errors = validateReadQRCodeStep(step);
    expect(errors).toHaveLength(0);
  });

  test('ska kräva bildsökväg för fil-källa', () => {
    const step = {
      ...createReadQRCodeStep(),
      imageSource: 'file' as const,
      imagePath: ''
    };
    const errors = validateReadQRCodeStep(step);
    expect(errors).toContain('Bildsökväg är obligatorisk när bildkälla är fil');
  });

  test('ska kräva bildvariabel för variabel-källa', () => {
    const step = {
      ...createReadQRCodeStep(),
      imageSource: 'variable' as const,
      imageVariable: ''
    };
    const errors = validateReadQRCodeStep(step);
    expect(errors).toContain('Bildvariabel är obligatorisk när bildkälla är variabel');
  });
});
```

## Steg 11: Testa Kompilering

```bash
# Kontrollera TypeScript
npm run type-check

# Bygg projektet
npm run build

# Kör tester
npm test
```

## Steg 12: Manuell Testning

1. Starta utvecklingsservern:
```bash
npm run dev
```

2. Skapa nytt flöde
3. Lägg till `takeScreenshot` steg
4. Lägg till `readQRCode` steg
5. Konfigurera steget
6. Kör flödet och verifiera resultat

## Steg 13: Dokumentation

Uppdatera dokumentation:

1. Lägg till i `docs/api/step-types.md`
2. Uppdatera `AI_STEP_IMPLEMENTATION_GUIDE.md`
3. Skapa exempel i `docs/examples/`

## Resultat

Nu har vi skapat ett komplett nytt RPA-steg som:

✅ Följer alla kodkonventioner
✅ Använder templates för konsistens
✅ Har fullständig validering
✅ Inkluderar UI-komponenter
✅ Har tester
✅ Är dokumenterat
✅ Fungerar i produktionen

## Lärdomar

Denna workflow visar hur utvecklarverktygen arbetar tillsammans:

1. **Templates** ger konsistent struktur
2. **Generator-script** sparar tid
3. **Dokumentation** ger vägledning
4. **Konventioner** säkerställer kvalitet
5. **Tester** validerar funktionalitet

## Nästa Steg

- Skapa fler AI-baserade steg
- Förbättra felhantering
- Optimera prestanda
- Lägg till fler test-scenarier
- Dokumentera best practices
