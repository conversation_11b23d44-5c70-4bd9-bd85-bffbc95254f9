import { StepDefinition, StepCategory, STEP_CATEGORIES } from './categories'

export const interactionSteps: StepDefinition[] = [
  {
    type: 'click',
    name: 'Click',
    icon: '👆',
    description: 'Click on an element'
  },
  {
    type: 'fill',
    name: 'Fill',
    icon: '✏️',
    description: 'Fill an input field'
  },
  {
    type: 'type',
    name: 'Type',
    icon: '⌨️',
    description: 'Type text into an element'
  },
  {
    type: 'selectOption',
    name: 'Select Option',
    icon: '📋',
    description: 'Select option from dropdown'
  },
  {
    type: 'check',
    name: 'Check',
    icon: '☑️',
    description: 'Check a checkbox'
  },
  {
    type: 'uncheck',
    name: 'Uncheck',
    icon: '☐',
    description: 'Uncheck a checkbox'
  },
  {
    type: 'conditionalClick',
    name: 'Conditional Click',
    icon: '🔀',
    description: 'Click element based on condition (exists/enabled/disabled)'
  },
  {
    type: 'ifElementExists',
    name: 'If Element Exists',
    icon: '❓',
    description: 'Check if element exists and execute conditional logic'
  }
]

export const interactionCategory: StepCategory = {
  name: STEP_CATEGORIES.INTERACTIONS,
  icon: '👆',
  steps: interactionSteps
}
