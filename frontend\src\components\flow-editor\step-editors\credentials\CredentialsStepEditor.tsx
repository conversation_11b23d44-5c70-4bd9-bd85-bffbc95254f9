import { useState, useEffect } from 'react'
import { validateStep, FillPasswordStep, Fill2FAStep, Credential } from '@rpa-project/shared'
import { BaseStepEditorProps, StepEditorLayout, CommonStepFields } from '../base'
import { SelectorField, SelectField } from '../base/FieldComponents'
import { credentialApi } from '../../../../services/api'

type CredentialsStep = FillPasswordStep | Fill2FAStep

interface CredentialsStepEditorProps extends BaseStepEditorProps {
  step: CredentialsStep
}

export function CredentialsStepEditor({
  step,
  onSave,
  onCancel,
  compact = false
}: CredentialsStepEditorProps) {
  const [editedStep, setEditedStep] = useState<CredentialsStep>({ ...step })
  const [errors, setErrors] = useState<string[]>([])
  const [credentials, setCredentials] = useState<Credential[]>([])
  const [loadingCredentials, setLoadingCredentials] = useState(false)

  useEffect(() => {
    if (editedStep.type === 'fillPassword' || editedStep.type === 'fill2FA') {
      loadCredentials()
    }
  }, [editedStep.type])

  const loadCredentials = async () => {
    try {
      setLoadingCredentials(true)
      const response = await credentialApi.getCredentials()
      if (response.success && response.data) {
        setCredentials(response.data)
      }
    } catch (error) {
      console.error('Error loading credentials:', error)
    } finally {
      setLoadingCredentials(false)
    }
  }

  const handleSave = () => {
    const validation = validateStep(editedStep)
    if (!validation.isValid) {
      setErrors(validation.errors.map(e => e.message))
      return
    }

    setErrors([])
    onSave(editedStep)
  }

  const updateStep = (updates: Partial<CredentialsStep>) => {
    setEditedStep(prev => ({ ...prev, ...updates } as CredentialsStep))
  }

  const renderCredentialsFields = () => {
    switch (editedStep.type) {
      case 'fillPassword':
        const passwordStep = editedStep as FillPasswordStep
        const passwordCredentials = credentials.filter(c => c.type === 'password')
        
        return (
          <>
            <SelectorField
              label="Element Selector"
              value={passwordStep.selector || ''}
              onChange={(selector) => updateStep({ selector })}
              placeholder="input[type='password'], #password"
              compact={compact}
            />
            {loadingCredentials ? (
              <div style={{ 
                padding: compact ? '0.375rem 0.5rem' : '0.5rem 0.75rem',
                color: '#6b7280',
                fontSize: compact ? '0.75rem' : '0.875rem'
              }}>
                Loading credentials...
              </div>
            ) : (
              <SelectField
                label="Credential"
                value={passwordStep.credentialId || ''}
                onChange={(value) => updateStep({ credentialId: value })}
                options={[
                  { value: '', label: 'Select a password credential' },
                  ...passwordCredentials.map((credential) => ({
                    value: credential.id,
                    label: `${credential.name} (${(credential as any).username})`
                  }))
                ]}
                compact={compact}
              />
            )}
            {passwordCredentials.length === 0 && !loadingCredentials && (
              <div style={{ 
                fontSize: '0.75rem', 
                color: '#dc2626', 
                marginTop: '0.25rem',
                padding: '0.5rem',
                backgroundColor: '#fef2f2',
                borderRadius: '0.25rem'
              }}>
                No password credentials found. Create one in the Credentials page.
              </div>
            )}
          </>
        )

      case 'fill2FA':
        const twoFAStep = editedStep as Fill2FAStep
        const twoFactorCredentials = credentials.filter(c => c.type === '2fa')
        
        return (
          <>
            <SelectorField
              label="Element Selector"
              value={twoFAStep.selector || ''}
              onChange={(selector) => updateStep({ selector })}
              placeholder="input, #totp-code"
              compact={compact}
            />
            {loadingCredentials ? (
              <div style={{ 
                padding: compact ? '0.375rem 0.5rem' : '0.5rem 0.75rem',
                color: '#6b7280',
                fontSize: compact ? '0.75rem' : '0.875rem'
              }}>
                Loading credentials...
              </div>
            ) : (
              <SelectField
                label="Credential"
                value={twoFAStep.credentialId || ''}
                onChange={(value) => updateStep({ credentialId: value })}
                options={[
                  { value: '', label: 'Select a 2FA credential' },
                  ...twoFactorCredentials.map((credential) => ({
                    value: credential.id,
                    label: credential.name
                  }))
                ]}
                compact={compact}
              />
            )}
            {twoFactorCredentials.length === 0 && !loadingCredentials && (
              <div style={{ 
                fontSize: '0.75rem', 
                color: '#dc2626', 
                marginTop: '0.25rem',
                padding: '0.5rem',
                backgroundColor: '#fef2f2',
                borderRadius: '0.25rem'
              }}>
                No 2FA credentials found. Create one in the Credentials page.
              </div>
            )}
          </>
        )

      default:
        return (
          <div className="text-gray-500">
            No specific configuration available for this step type.
          </div>
        )
    }
  }

  const getTitle = () => {
    const stepNames = {
      fillPassword: 'Fill Password',
      fill2FA: 'Fill 2FA'
    }
    
    const stepName = stepNames[editedStep.type as keyof typeof stepNames] || editedStep.type
    return compact ? `Konfigurera ${stepName}-steg` : `Edit ${stepName} Step`
  }

  return (
    <StepEditorLayout
      title={getTitle()}
      errors={errors}
      onSave={handleSave}
      onCancel={onCancel}
      compact={compact}
    >
      {renderCredentialsFields()}
      <CommonStepFields 
        step={editedStep} 
        updateStep={updateStep} 
        compact={compact} 
      />
    </StepEditorLayout>
  )
}
