import { RpaStepBase } from './base';

// File handling steps
export interface DownloadFileStep extends RpaStepBase {
  type: 'downloadFile';
  triggerSelector?: string; // Optional selector to click to trigger download
  filename?: string; // Optional custom filename, if not provided uses suggested filename
  variableName?: string; // Variable name to store base64 content
  saveToFile?: boolean; // Whether to save file to disk (default: false)
  forceDownload?: boolean; // Whether to add download attribute to force download instead of opening in browser
}
