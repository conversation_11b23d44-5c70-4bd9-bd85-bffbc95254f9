# Exempel: Skapa Custom Runner

Detta exempel visar hur man skapar en ny runner för att integrera med externa API:er.

## Scenario

Vi vill skapa en runner som kan integrera med Slack API för att skicka meddelanden och hämta data från kanaler.

**Runner-namn**: `SlackRunner`
**Steg-typer**: `sendSlackMessage`, `getSlackChannels`, `uploadSlackFile`

## Steg 1: Skapa Runner-struktur

```bash
mkdir -p backend/src/runners/slack
mkdir -p backend/src/runners/slack/stepExecutors
mkdir -p backend/src/runners/slack/__tests__
```

## Steg 2: Definiera Step-typer

Skapa `shared/src/types/steps/slack.ts`:

```typescript
import { RpaStepBase } from './base';

export interface SendSlackMessageStep extends RpaStepBase {
  type: 'sendSlackMessage';
  channel: string;
  message: string;
  threadTs?: string;
  asUser?: boolean;
}

export interface GetSlackChannelsStep extends RpaStepBase {
  type: 'getSlackChannels';
  includeArchived?: boolean;
  limit?: number;
  variableName?: string;
}

export interface UploadSlackFileStep extends RpaStepBase {
  type: 'uploadSlackFile';
  channel: string;
  filePath: string;
  title?: string;
  comment?: string;
}
```

## Steg 3: Skapa Runner-klass

Skapa `backend/src/runners/slack/SlackRunner.ts`:

```typescript
import { BaseRunner } from '../base/BaseRunner';
import { RpaStep } from '../../../shared/src/types/steps';
import { ExecutionContext } from '../types';
import { WebClient } from '@slack/web-api';
import { SendSlackMessageStep, GetSlackChannelsStep, UploadSlackFileStep } from '../../../shared/src/types/steps/slack';

export class SlackRunner extends BaseRunner {
  public readonly runnerType = 'slack' as const;
  
  private client: WebClient;
  private isInitialized = false;

  constructor() {
    super();
    this.initializeRunner();
  }

  private async initializeRunner(): Promise<void> {
    try {
      const token = process.env.SLACK_BOT_TOKEN;
      if (!token) {
        throw new Error('SLACK_BOT_TOKEN miljövariabel saknas');
      }

      this.client = new WebClient(token);
      
      // Testa anslutning
      await this.client.auth.test();
      this.isInitialized = true;
      
      this.logInfo('SlackRunner initialiserad');
    } catch (error) {
      this.logError(`Fel vid initialisering av SlackRunner: ${error}`);
      throw error;
    }
  }

  public canExecuteStep(step: RpaStep): boolean {
    const supportedSteps = [
      'sendSlackMessage',
      'getSlackChannels',
      'uploadSlackFile',
    ];
    
    return supportedSteps.includes(step.type);
  }

  public async executeStep(step: RpaStep, context: ExecutionContext): Promise<void> {
    if (!this.canExecuteStep(step)) {
      throw new Error(`SlackRunner kan inte hantera steg av typ: ${step.type}`);
    }

    if (!this.isInitialized) {
      throw new Error('SlackRunner är inte initialiserad');
    }

    this.logInfo(`Utför ${step.type}: ${step.name}`);

    try {
      switch (step.type) {
        case 'sendSlackMessage':
          await this.executeSendSlackMessageStep(step as SendSlackMessageStep, context);
          break;
        case 'getSlackChannels':
          await this.executeGetSlackChannelsStep(step as GetSlackChannelsStep, context);
          break;
        case 'uploadSlackFile':
          await this.executeUploadSlackFileStep(step as UploadSlackFileStep, context);
          break;
        default:
          throw new Error(`Ostödd steg-typ: ${step.type}`);
      }

      this.logInfo(`${step.type} slutfört framgångsrikt`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Okänt fel';
      this.logError(`Fel vid ${step.type}: ${errorMessage}`);
      throw error;
    }
  }

  public async start(): Promise<void> {
    try {
      this.logInfo('Startar SlackRunner');
      
      if (!this.isInitialized) {
        await this.initializeRunner();
      }
      
      this.logInfo('SlackRunner startad');
    } catch (error) {
      this.logError(`Fel vid start av SlackRunner: ${error}`);
      throw error;
    }
  }

  public async stop(): Promise<void> {
    try {
      this.logInfo('Stoppar SlackRunner');
      
      // Cleanup om nödvändigt
      
      this.logInfo('SlackRunner stoppad');
    } catch (error) {
      this.logError(`Fel vid stopp av SlackRunner: ${error}`);
    }
  }

  public getStatus(): { isReady: boolean; details: string } {
    return {
      isReady: this.isInitialized,
      details: this.isInitialized ? 'Slack API ansluten' : 'Slack API inte ansluten'
    };
  }

  private async executeSendSlackMessageStep(
    step: SendSlackMessageStep, 
    context: ExecutionContext
  ): Promise<void> {
    const result = await this.client.chat.postMessage({
      channel: step.channel,
      text: step.message,
      thread_ts: step.threadTs,
      as_user: step.asUser || false,
    });

    // Spara meddelande-info i context
    context.variables['var-slack-message-ts'] = result.ts;
    context.variables['var-slack-message-channel'] = result.channel;
  }

  private async executeGetSlackChannelsStep(
    step: GetSlackChannelsStep, 
    context: ExecutionContext
  ): Promise<void> {
    const result = await this.client.conversations.list({
      exclude_archived: !step.includeArchived,
      limit: step.limit || 100,
    });

    const channels = result.channels || [];
    const variableName = step.variableName || 'var-slack-channels';
    
    context.variables[variableName] = channels;
    context.variables[`${variableName}-count`] = channels.length;
  }

  private async executeUploadSlackFileStep(
    step: UploadSlackFileStep, 
    context: ExecutionContext
  ): Promise<void> {
    const fs = require('fs');
    
    if (!fs.existsSync(step.filePath)) {
      throw new Error(`Fil inte hittad: ${step.filePath}`);
    }

    const result = await this.client.files.upload({
      channels: step.channel,
      file: fs.createReadStream(step.filePath),
      title: step.title,
      initial_comment: step.comment,
    });

    // Spara fil-info i context
    context.variables['var-slack-file-id'] = result.file?.id;
    context.variables['var-slack-file-url'] = result.file?.url_private;
  }
}
```

## Steg 4: Skapa Validators

Skapa `shared/src/validators/steps/slack.ts`:

```typescript
import { SendSlackMessageStep, GetSlackChannelsStep, UploadSlackFileStep } from '../../types/steps/slack';

export function validateSendSlackMessageStep(step: SendSlackMessageStep): string[] {
  const errors: string[] = [];

  if (!step.channel?.trim()) {
    errors.push('Kanal är obligatorisk');
  }

  if (!step.message?.trim()) {
    errors.push('Meddelande är obligatoriskt');
  }

  return errors;
}

export function validateGetSlackChannelsStep(step: GetSlackChannelsStep): string[] {
  const errors: string[] = [];

  if (step.limit !== undefined && (step.limit < 1 || step.limit > 1000)) {
    errors.push('Limit måste vara mellan 1 och 1000');
  }

  return errors;
}

export function validateUploadSlackFileStep(step: UploadSlackFileStep): string[] {
  const errors: string[] = [];

  if (!step.channel?.trim()) {
    errors.push('Kanal är obligatorisk');
  }

  if (!step.filePath?.trim()) {
    errors.push('Filsökväg är obligatorisk');
  }

  return errors;
}

export function createSendSlackMessageStep(): SendSlackMessageStep {
  return {
    id: '',
    type: 'sendSlackMessage',
    name: 'Skicka Slack-meddelande',
    description: '',
    channel: '#general',
    message: '',
    asUser: false,
  };
}

export function createGetSlackChannelsStep(): GetSlackChannelsStep {
  return {
    id: '',
    type: 'getSlackChannels',
    name: 'Hämta Slack-kanaler',
    description: '',
    includeArchived: false,
    limit: 100,
    variableName: 'slackChannels',
  };
}

export function createUploadSlackFileStep(): UploadSlackFileStep {
  return {
    id: '',
    type: 'uploadSlackFile',
    name: 'Ladda upp fil till Slack',
    description: '',
    channel: '#general',
    filePath: '',
  };
}
```

## Steg 5: Skapa Editor-komponenter

Skapa `frontend/src/components/flow-editor/step-editors/slack/SendSlackMessageStepEditor.tsx`:

```typescript
import React from 'react';
import { SendSlackMessageStep } from '../../../../../shared/src/types/steps/slack';
import { BaseStepEditorProps } from '../base/BaseStepEditor';

interface SendSlackMessageStepEditorProps extends BaseStepEditorProps {
  step: SendSlackMessageStep;
}

export const SendSlackMessageStepEditor: React.FC<SendSlackMessageStepEditorProps> = ({
  step,
  onStepChange,
  variables = []
}) => {
  const handleChange = (field: keyof SendSlackMessageStep, value: any) => {
    onStepChange({
      ...step,
      [field]: value
    });
  };

  return (
    <div className="step-editor">
      <div className="form-row">
        <div className="form-group">
          <label>Namn</label>
          <input
            type="text"
            value={step.name || ''}
            onChange={(e) => handleChange('name', e.target.value)}
            className="form-input"
          />
        </div>
      </div>

      <div className="form-row">
        <div className="form-group">
          <label>Kanal</label>
          <input
            type="text"
            value={step.channel || ''}
            onChange={(e) => handleChange('channel', e.target.value)}
            placeholder="#general, @user, C1234567890"
            className="form-input"
          />
        </div>
      </div>

      <div className="form-row">
        <div className="form-group">
          <label>Meddelande</label>
          <textarea
            value={step.message || ''}
            onChange={(e) => handleChange('message', e.target.value)}
            placeholder="Ditt meddelande här..."
            className="form-textarea"
            rows={4}
          />
        </div>
      </div>

      <div className="form-row">
        <div className="form-group">
          <label>Thread Timestamp (valfritt)</label>
          <input
            type="text"
            value={step.threadTs || ''}
            onChange={(e) => handleChange('threadTs', e.target.value)}
            placeholder="1234567890.123456"
            className="form-input"
          />
        </div>
        <div className="form-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={step.asUser || false}
              onChange={(e) => handleChange('asUser', e.target.checked)}
            />
            Skicka som användare
          </label>
        </div>
      </div>
    </div>
  );
};
```

## Steg 6: Registrera Runner

Uppdatera `backend/src/runners/registry/stepTypes.ts`:

```typescript
export const STEP_RUNNER_MAPPING = {
  // ... befintliga mappningar
  
  // Slack steg
  sendSlackMessage: 'slack',
  getSlackChannels: 'slack',
  uploadSlackFile: 'slack',
} as const;

export type RunnerType = 
  | 'playwright' 
  | 'ai' 
  | 'slack'  // Lägg till här
  | 'api';
```

Uppdatera `backend/src/runners/factory/RunnerFactory.ts`:

```typescript
import { SlackRunner } from '../slack/SlackRunner';

export class RunnerFactory {
  createRunner(type: RunnerType): IRunner {
    switch (type) {
      case 'playwright':
        return new PlaywrightRunner();
      case 'ai':
        return new AIRunner();
      case 'slack':
        return new SlackRunner();
      default:
        throw new Error(`Unknown runner type: ${type}`);
    }
  }
}
```

## Steg 7: Installera Dependencies

```bash
cd backend
npm install @slack/web-api
```

## Steg 8: Konfigurera Miljövariabler

Lägg till i `.env`:

```env
SLACK_BOT_TOKEN=xoxb-your-bot-token-here
```

## Steg 9: Skapa Tester

Skapa `backend/src/runners/slack/__tests__/SlackRunner.test.ts`:

```typescript
import { SlackRunner } from '../SlackRunner';
import { ExecutionContext } from '../../types';

// Mock Slack client
jest.mock('@slack/web-api');

describe('SlackRunner', () => {
  let runner: SlackRunner;
  let mockContext: ExecutionContext;

  beforeEach(() => {
    process.env.SLACK_BOT_TOKEN = 'xoxb-test-token';
    runner = new SlackRunner();
    mockContext = {
      variables: {},
      flowId: 'test-flow',
      executionId: 'test-execution',
      customerId: 'test-customer'
    };
  });

  afterEach(async () => {
    await runner.stop();
    delete process.env.SLACK_BOT_TOKEN;
  });

  test('ska skapa runner-instans', () => {
    expect(runner).toBeInstanceOf(SlackRunner);
    expect(runner.runnerType).toBe('slack');
  });

  test('ska identifiera stödda steg', () => {
    const supportedStep = {
      id: 'test-1',
      type: 'sendSlackMessage',
      name: 'Test Message'
    };

    expect(runner.canExecuteStep(supportedStep as any)).toBe(true);
  });

  // Lägg till fler tester...
});
```

## Steg 10: Testa Integration

1. Skapa Slack app och bot token
2. Konfigurera miljövariabler
3. Bygg och starta projektet
4. Skapa flöde med Slack-steg
5. Testa att meddelanden skickas

## Resultat

Nu har du en fullt fungerande SlackRunner som kan:

- Skicka meddelanden till Slack-kanaler
- Hämta lista över kanaler
- Ladda upp filer
- Hantera fel gracefully
- Spara resultat i variabler

## Nästa Steg

- Lägg till fler Slack API-funktioner
- Implementera rate limiting
- Skapa UI för Slack-inställningar
- Dokumentera alla steg-typer
- Skapa fler integrationstester
