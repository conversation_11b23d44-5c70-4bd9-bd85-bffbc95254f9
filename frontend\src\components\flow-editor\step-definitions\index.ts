// Export types and categories
export * from './categories'

// Export individual step definitions
export * from './navigation'
export * from './interaction'
export * from './waiting'
export * from './credentials'
export * from './extraction'
export * from './ai'
export * from './api'

// Import all categories for easy access
import { navigationCategory } from './navigation'
import { interactionCategory } from './interaction'
import { waitingCategory } from './waiting'
import { credentialsCategory } from './credentials'
import { extractionCategory } from './extraction'
import { aiCategory } from './ai'
import { apiCategory } from './api'

// Export all step categories as an array
export const stepCategories = [
  navigationCategory,
  interactionCategory,
  waitingCategory,
  credentialsCategory,
  extractionCategory,
  aiCategory,
  apiCategory
]

// Export all step categories including future ones
export const allStepCategories = [
  navigationCategory,
  interactionCategory,
  waitingCategory,
  credentialsCategory,
  extractionCategory,
  aiCategory,
  apiCategory
]
