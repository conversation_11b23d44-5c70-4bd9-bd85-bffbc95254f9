// Template för ny RPA step-typ
// <PERSON><PERSON><PERSON>t {{STEP_TYPE}}, {{STEP_CATEGORY}}, {{STEP_DESCRIPTION}} med faktiska värden

import { RpaStepBase } from './base';

/**
 * {{STEP_DESCRIPTION}}
 */
export interface {{PASCAL_CASE}}Step extends RpaStepBase {
  type: '{{STEP_TYPE}}';
  
  // Lägg till step-specifika properties här
  // Exempel:
  // selector?: string;
  // value?: string;
  // timeout?: number;
  // waitForSelector?: boolean;
  
  // För AI-steg:
  // prompt?: string;
  // model?: string;
  
  // För API-steg:
  // url?: string;
  // method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  // headers?: Record<string, string>;
  // body?: any;
}

// Lägg till i RpaStep union type i index.ts:
// | {{PASCAL_CASE}}Step
