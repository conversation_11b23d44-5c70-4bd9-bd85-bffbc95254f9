#!/bin/bash

# This script now only prepares the environment for VNC services
# VNC services will be started dynamically by the VNCService when needed

echo "🖥️ Preparing environment for dynamic VNC services..."

# Set DISPLAY environment variable for potential Xvfb usage
export DISPLAY=:99

# Ensure required directories exist
mkdir -p /tmp/.X11-unix

# Start the main application
echo "🚀 Starting RPA Backend..."
cd /app/backend
exec node dist/index.js
