# Multi-stage build for better optimization
# Build stage
FROM node:20-alpine as builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY backend/package*.json ./backend/
COPY shared/package*.json ./shared/

# Install build dependencies for native modules
RUN apk add --no-cache python3 make g++

# Install all dependencies (including dev dependencies for building)
RUN npm ci

# Copy shared types and build
COPY shared/ ./shared/
COPY tsconfig.json ./
WORKDIR /app/shared
RUN npm run build

# Copy backend source and build
WORKDIR /app
COPY backend/ ./backend/
WORKDIR /app/backend
RUN npm run build

# Production stage
FROM mcr.microsoft.com/playwright:v1.40.0-focal

# Create non-root user for security
RUN groupadd -r rpauser && useradd -r -g rpauser -m -d /home/<USER>

# Set working directory
WORKDIR /app

# Copy package files for production dependencies
COPY package*.json ./
COPY backend/package*.json ./backend/
COPY shared/package*.json ./shared/

# Install only production dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy built shared package
COPY --from=builder /app/shared/dist ./shared/dist
COPY --from=builder /app/shared/package.json ./shared/

# Copy built backend
COPY --from=builder /app/backend/dist ./backend/dist
COPY --from=builder /app/backend/package.json ./backend/

# Copy startup script for Xvfb support
COPY backend/start-with-xvfb.sh ./start-with-xvfb.sh

# Create directories with proper permissions and make startup script executable
RUN mkdir -p /app/screenshots /app/downloads /app/data && \
    chmod +x /app/start-with-xvfb.sh && \
    chown -R rpauser:rpauser /app

# Install Chromium browser and dependencies
RUN npx playwright install chromium && \
    npx playwright install-deps chromium

# Install additional packages for VNC support and noVNC
RUN apt-get update && \
    apt-get install -y curl xvfb x11vnc fluxbox git python3 python3-pip python3-numpy && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Install noVNC for web-based VNC access
RUN git clone https://github.com/novnc/noVNC.git /opt/noVNC && \
    git clone https://github.com/novnc/websockify /opt/noVNC/utils/websockify && \
    cd /opt/noVNC/utils/websockify && \
    python3 setup.py install

# Switch to non-root user
USER rpauser

# Expose port
EXPOSE 44300

# Set environment variables
ENV NODE_ENV=production
ENV REDIS_URL=redis://redis:6379
ENV HOME=/home/<USER>
ENV npm_config_cache=/tmp/.npm

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f -k https://localhost:44300/health || exit 1

# Start the application with Xvfb support
WORKDIR /app
CMD ["./start-with-xvfb.sh"]
