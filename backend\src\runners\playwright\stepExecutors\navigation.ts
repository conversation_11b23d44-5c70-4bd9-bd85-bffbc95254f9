import { Page } from 'playwright';
import { RpaStep, ExecutionLog } from '@rpa-project/shared';
import { StepExecutionResult } from '../../base';

/**
 * Navigation step executors for PlaywrightRunner
 */

export interface NavigationExecutorContext {
  page: Page;
  variables: Record<string, any>;
  onLog: (log: Omit<ExecutionLog, 'timestamp'>) => void;
  interpolateVariables: (text: string, variables: Record<string, any>) => string;
}

/**
 * Execute navigate step
 */
export async function executeNavigate(
  step: RpaStep & { url: string; waitUntil?: 'load' | 'domcontentloaded' | 'networkidle' | 'commit' },
  context: NavigationExecutorContext
): Promise<StepExecutionResult> {
  const { page, variables, onLog, interpolateVariables } = context;
  const timeout = step.timeout || 30000;

  const interpolatedUrl = interpolateVariables(step.url, variables);
  await page.goto(interpolatedUrl, {
    waitUntil: step.waitUntil || 'load',
    timeout
  });

  onLog({
    level: 'info',
    message: `Navigated to ${interpolatedUrl}`,
    stepId: step.id
  });

  return { success: true };
}

/**
 * Execute goBack step
 */
export async function executeGoBack(
  step: RpaStep,
  context: NavigationExecutorContext
): Promise<StepExecutionResult> {
  const { page, onLog } = context;
  const timeout = step.timeout || 30000;

  await page.goBack({ timeout });
  onLog({
    level: 'info',
    message: 'Navigated back',
    stepId: step.id
  });

  return { success: true };
}

/**
 * Execute goForward step
 */
export async function executeGoForward(
  step: RpaStep,
  context: NavigationExecutorContext
): Promise<StepExecutionResult> {
  const { page, onLog } = context;
  const timeout = step.timeout || 30000;

  await page.goForward({ timeout });
  onLog({
    level: 'info',
    message: 'Navigated forward',
    stepId: step.id
  });

  return { success: true };
}

/**
 * Execute reload step
 */
export async function executeReload(
  step: RpaStep,
  context: NavigationExecutorContext
): Promise<StepExecutionResult> {
  const { page, onLog } = context;
  const timeout = step.timeout || 30000;

  await page.reload({ timeout });
  onLog({
    level: 'info',
    message: 'Page reloaded',
    stepId: step.id
  });

  return { success: true };
}
