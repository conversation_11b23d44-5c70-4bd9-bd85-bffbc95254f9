import { useState, useEffect, useRef, useCallback } from 'react'
import { Link } from 'react-router-dom'
import { FlowExecution, RpaFlow, Customer } from '@rpa-project/shared'
import { executionApi, flowApi, customerApi } from '../services/api'

export function ExecutionList() {
  const [executions, setExecutions] = useState<FlowExecution[]>([])
  const [flows, setFlows] = useState<RpaFlow[]>([])
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(true)
  const [loadingMore, setLoadingMore] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [hasMore, setHasMore] = useState(true)
  const [offset, setOffset] = useState(0)
  const [searchQuery, setSearchQuery] = useState('')
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')
  const observerRef = useRef<IntersectionObserver | null>(null)

  const ITEMS_PER_PAGE = 20

  // Intersection observer callback for infinite scroll
  const lastExecutionElementRefCallback = useCallback((node: HTMLTableRowElement | null) => {
    if (loading || loadingMore) return
    if (observerRef.current) observerRef.current.disconnect()

    observerRef.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting && hasMore) {
        loadExecutions()
      }
    })

    if (node) observerRef.current.observe(node)
  }, [loading, loadingMore, hasMore])

  useEffect(() => {
    loadInitialData()

    // Auto-refresh every 10 seconds (increased from 5 to reduce server load with pagination)
    // const interval = setInterval(() => {
    //   if (!loadingMore) {
    //     setExecutions([])
    //     setOffset(0)
    //     setHasMore(true)
    //     loadInitialData()
    //   }
    // }, 10000)

    // return () => clearInterval(interval)
  }, [])

  // Reset and reload when date filters change (not search query)
  useEffect(() => {
    setExecutions([])
    setOffset(0)
    setHasMore(true)
    loadInitialData()
  }, [startDate, endDate])

  const loadInitialData = async () => {
    setLoading(true)
    try {
      const [flowsResponse, customersResponse] = await Promise.all([
        flowApi.getFlows(),
        customerApi.getCustomers()
      ])

      setFlows(flowsResponse.data || [])
      setCustomers(customersResponse.data || [])

      // Load first page of executions
      await loadExecutions(0, true)
      setError(null)
    } catch (err) {
      setError('Misslyckades att ladda data')
      console.error('Error loading data:', err)
    } finally {
      setLoading(false)
    }
  }

  const loadExecutions = async (currentOffset: number = offset, isInitial: boolean = false) => {
    if (!isInitial && loadingMore) return

    setLoadingMore(true)
    try {
      const params: any = {
        limit: ITEMS_PER_PAGE,
        offset: currentOffset
      }

      // Format dates for API (add time to make them inclusive)
      if (startDate) {
        params.startDate = `${startDate}T00:00:00.000Z`
      }
      if (endDate) {
        params.endDate = `${endDate}T23:59:59.999Z`
      }

      const response = await executionApi.getExecutions(params)
      const newExecutions = response.data || []

      if (isInitial) {
        setExecutions(newExecutions)
      } else {
        setExecutions(prev => [...prev, ...newExecutions])
      }

      setHasMore(newExecutions.length === ITEMS_PER_PAGE)
      setOffset(currentOffset + newExecutions.length)
      setError(null)
    } catch (err) {
      setError('Misslyckades att ladda körningar')
      console.error('Error loading executions:', err)
    } finally {
      setLoadingMore(false)
    }
  }



  const handleCancelExecution = async (id: string) => {
    if (!confirm('Är du säker på att du vill avbryta denna körning?')) {
      return
    }

    try {
      await executionApi.cancelExecution(id)
      await loadExecutions() // Refresh list
    } catch (err) {
      setError('Misslyckades att avbryta körning')
      console.error('Error canceling execution:', err)
    }
  }

  const getFlowName = (flowId: string): string => {
    const flow = flows.find(f => f.id === flowId)
    return flow?.name || `Okänt skript (${flowId.slice(0, 8)}...)`
  }

  const getCustomerInfo = (customerId: string): { customerNumber: string; customerName: string } => {
    const customer = customers.find(c => c.id === customerId)
    return {
      customerNumber: customer?.customerNumber || 'Okänt',
      customerName: customer?.name || 'Okänd kund'
    }
  }

  // Filter executions based on search query
  const filteredExecutions = executions.filter(execution => {
    if (!searchQuery.trim()) return true

    const query = searchQuery.toLowerCase()
    const flowName = getFlowName(execution.flowId).toLowerCase()
    const customerInfo = getCustomerInfo(execution.customerId)

    // Search in flow name
    if (flowName.includes(query)) return true

    // Search in customer number and name
    if (customerInfo.customerNumber.toLowerCase().includes(query)) return true
    if (customerInfo.customerName.toLowerCase().includes(query)) return true

    return false
  })

  const formatDuration = (start: Date, end?: Date) => {
    const startTime = new Date(start).getTime()
    const endTime = end ? new Date(end).getTime() : Date.now()
    const duration = endTime - startTime

    if (duration < 1000) return `${duration}ms`
    if (duration < 60000) return `${Math.round(duration / 1000)}s`
    return `${Math.round(duration / 60000)}m`
  }

  const getStatusButtonClass = (status: string) => {
    const baseClass = "status-button"

    switch (status) {
      case 'completed':
        return `${baseClass} status-completed`
      case 'running':
        return `${baseClass} status-running`
      case 'pending':
        return `${baseClass} status-scheduled`
      case 'failed':
        return `${baseClass} status-failed`
      case 'cancelled':
        return `${baseClass} status-failed`
      default:
        return `${baseClass} status-scheduled`
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Slutförd'
      case 'running':
        return 'Körs'
      case 'pending':
        return 'Väntar'
      case 'failed':
        return 'Misslyckad'
      case 'cancelled':
        return 'Avbruten'
      default:
        return status
    }
  }

  if (loading && executions.length === 0) {
    return (
      <div className="loading-container">
        <div className="loading-text">Laddar körningar...</div>
      </div>
    )
  }

  const getExecutionStats = () => {
    const total = executions.length
    const completed = executions.filter(e => e.status === 'completed').length
    const running = executions.filter(e => e.status === 'running').length
    const failed = executions.filter(e => e.status === 'failed').length
    const pending = executions.filter(e => e.status === 'pending').length

    return { total, completed, running, failed, pending }
  }

  const stats = getExecutionStats()

  return (
    <div className="dashboard-container">
      {/* Header */}
      <div className="dashboard-header">
        <div className="dashboard-header-content">
          <p className="dashboard-title">Loggar</p>
          <p className="dashboard-subtitle">
            Övervaka dina automatiseringskörningar och loggar.
          </p>
        </div>
        <div style={{
          display: 'flex',
          gap: '1rem',
          alignItems: 'center',
          flexWrap: 'wrap',
          justifyContent: 'flex-end'
        }}>
          <div style={{ position: 'relative' }}>
            <input
              type="text"
              placeholder="🔎 Sök körningar, kunder..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="search-input"
            />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                style={{
                  position: 'absolute',
                  right: '0.5rem',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  background: 'none',
                  border: 'none',
                  cursor: 'pointer',
                  fontSize: '1rem',
                  color: '#666'
                }}
              >
                ✕
              </button>
            )}
          </div>

          <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>
            <label style={{ fontSize: '0.9rem', color: '#666' }}>Från:</label>
            <input
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              className="search-input"
              style={{ width: '150px' }}
            />
          </div>

          <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>
            <label style={{ fontSize: '0.9rem', color: '#666' }}>Till:</label>
            <input
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              className="search-input"
              style={{ width: '150px' }}
            />
          </div>

          {(startDate || endDate) && (
            <button
              onClick={() => {
                setStartDate('')
                setEndDate('')
              }}
              className="action-button-small secondary"
              title="Rensa datumfilter"
            >
              <span>Nollställ</span>
            </button>
          )}

          <button
            onClick={() => {
              setExecutions([])
              setOffset(0)
              setHasMore(true)
              loadInitialData()
            }}
            className="action-button secondary"
            disabled={loading}
          >
            <span>{loading ? 'Uppdaterar...' : 'Uppdatera'}</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      {executions.length > 0 && (
        <div className="stats-grid">
          <div className="stat-card">
            <p className="stat-label">Totalt antal körningar</p>
            <p className="stat-value">{stats.total}</p>
            <p className="stat-change">+0%</p>
          </div>
          <div className="stat-card">
            <p className="stat-label">Slutförda</p>
            <p className="stat-value">{stats.completed}</p>
            <p className="stat-change">+0%</p>
          </div>
          <div className="stat-card">
            <p className="stat-label">Körs nu</p>
            <p className="stat-value">{stats.running}</p>
            <p className="stat-change">+0%</p>
          </div>
          <div className="stat-card">
            <p className="stat-label">Misslyckade</p>
            <p className="stat-value">{stats.failed}</p>
            <p className="stat-change">+0%</p>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="error-card">
          <h3 className="error-title">Fel vid laddning av körningar</h3>
          <p className="error-message">{error}</p>
        </div>
      )}

      {/* Executions List */}
      {executions.length === 0 ? (
        <div className="empty-state-container">
          <div className="empty-state">
            <div className="empty-state-icon">🤖</div>
            <p className="empty-state-title">Inga körningar än</p>
            <p className="empty-state-subtitle">Kör ett flöde för att se resultat här</p>
            <Link to="/flows" className="action-button secondary centered">
              <span>Gå till flöde</span>
            </Link>
          </div>
        </div>
      ) : filteredExecutions.length === 0 ? (
        <div className="empty-state-container">
          <div className="empty-state">
            <div className="empty-state-icon">🔍</div>
            <p className="empty-state-title">Inga körningar hittades</p>
            <p className="empty-state-subtitle">Försök med en annan sökning eller ändra datumfilter</p>
            <button
              onClick={() => setSearchQuery('')}
              className="action-button secondary centered"
              style={{ marginTop: '1rem' }}
            >
              <span>Rensa sökning</span>
            </button>
          </div>
        </div>
      ) : (
        <>
          <h2 className="section-title">
            {searchQuery ? `Sökresultat (${filteredExecutions.length} av ${executions.length})` : `Senaste körningar (${executions.length}${hasMore ? '+' : ''})`}
          </h2>
          <div className="table-container">
            <div className="activity-table">
              <table className="table">
                <thead>
                  <tr>
                    <th>Flödesnamn</th>
                    <th>Kundnummer</th>
                    <th>Kundnamn</th>
                    <th>Status</th>
                    <th>Startad</th>
                    <th>Varaktighet</th>
                    <th>Loggar</th>
                    <th>Åtgärder</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredExecutions.map((execution, index) => (
                    <tr
                      key={execution.id}
                      ref={index === filteredExecutions.length - 1 ? lastExecutionElementRefCallback : null}
                    >
                      <td>
                        <Link
                          to={`/executions/${execution.id}`}
                          className="flow-name-link"
                        >
                          {getFlowName(execution.flowId)}
                        </Link>
                      </td>
                      <td className="secondary-text">
                        {getCustomerInfo(execution.customerId).customerNumber}
                      </td>
                      <td className="secondary-text">
                        {getCustomerInfo(execution.customerId).customerName}
                      </td>
                      <td>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                          <button className={getStatusButtonClass(execution.status)}>
                            <span>{getStatusText(execution.status)}</span>
                          </button>
                          {(execution as any).retryInfo && (execution as any).retryInfo.isRetry && (
                            <span className="retry-badge" title={`Retry-försök: ${(execution as any).retryInfo.attempts}/${(execution as any).retryInfo.maxAttempts}`}>
                              🔄 {(execution as any).retryInfo.attempts}
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="secondary-text">
                        {new Date(execution.startedAt).toLocaleString('sv-SE', {
                          year: 'numeric',
                          month: '2-digit',
                          day: '2-digit',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </td>
                      <td className="secondary-text">
                        {formatDuration(execution.startedAt, execution.completedAt)}
                      </td>
                      <td className="secondary-text">
                        {execution.logs.length}
                      </td>
                      <td>
                        <div className="flow-actions">
                          <Link
                            to={`/executions/${execution.id}`}
                            className="action-button-small secondary"
                            title="Visa detaljer"
                          >
                            <span>Visa</span>
                          </Link>
                          {(execution.status === 'pending' || execution.status === 'running') && (
                            <button
                              onClick={() => handleCancelExecution(execution.id)}
                              className="action-button-small danger"
                              title="Avbryt körning"
                            >
                              <span>Avbryt</span>
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Loading more indicator */}
          {loadingMore && (
            <div style={{
              textAlign: 'center',
              padding: '2rem',
              color: '#666'
            }}>
              <div>Laddar fler körningar...</div>
            </div>
          )}
        </>
      )}
    </div>
  )
}
