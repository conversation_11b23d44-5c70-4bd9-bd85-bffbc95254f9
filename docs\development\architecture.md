# RPA System Arkitektur

## Översikt

RPA-systemet är byggt med en modulär arkitektur som gör det enkelt att lägga till nya steg-typer och runners. Systemet består av tre huvudkomponenter:

- **Frontend**: React-baserad UI för att skapa och hantera RPA-flöden
- **Backend**: Node.js server som utför RPA-flöden
- **Shared**: Gemensamma typer och validatorer

## Systemarkitektur

```mermaid
graph TB
    subgraph "Frontend (React)"
        UI[Flow Designer UI]
        Editor[Step Editors]
        Toolbar[Step Toolbar]
    end
    
    subgraph "Backend (Node.js)"
        API[REST API]
        Executor[Flow Executor]
        Runners[Runners]
        Queue[BullMQ Queue]
    end
    
    subgraph "Shared Package"
        Types[TypeScript Types]
        Validators[Validators]
    end
    
    subgraph "External Services"
        Redis[(Redis)]
        OpenAI[OpenAI API]
        Browser[Playwright Browser]
    end
    
    UI --> API
    Editor --> Types
    Toolbar --> Types
    API --> Executor
    Executor --> Runners
    Runners --> Browser
    Runners --> OpenAI
    Queue --> Redis
    Validators --> Types
```

## Komponentarkitektur

### Frontend Struktur

```
frontend/src/
├── components/
│   ├── flow-editor/           # Flow designer komponenter
│   │   ├── step-editors/      # Step-specifika editors
│   │   ├── step-definitions/  # Step toolbar definitioner
│   │   └── FlowEditor.tsx     # Huvudkomponent
│   ├── common/               # Återanvändbara komponenter
│   └── pages/                # Sidor (Dashboard, FlowList, etc.)
├── hooks/                    # Custom React hooks
├── services/                 # API-anrop
└── utils/                    # Hjälpfunktioner
```

### Backend Struktur

```
backend/src/
├── routes/                   # Express routes
├── services/                 # Business logic
├── runners/                  # RPA runners
│   ├── base/                # Bas-klasser och interfaces
│   ├── playwright/          # Playwright runner
│   ├── ai/                  # AI runner
│   ├── registry/            # Runner registry
│   └── factory/             # Runner factory
├── queue/                   # BullMQ job processing
└── utils/                   # Hjälpfunktioner
```

### Shared Package Struktur

```
shared/src/
├── types/
│   ├── steps/               # Step-typer per kategori
│   ├── runners.ts           # Runner interfaces
│   ├── flows.ts             # Flow-typer
│   └── execution.ts         # Execution-typer
├── validators/
│   ├── steps/               # Step-validatorer
│   └── flows.ts             # Flow-validatorer
└── index.ts                 # Export allt
```

## Runner Arkitektur

### Runner Interface

Alla runners implementerar `IRunner` interface:

```typescript
interface IRunner {
  runnerType: string;
  canExecuteStep(step: RpaStep): boolean;
  executeStep(step: RpaStep, context: ExecutionContext): Promise<void>;
  start(): Promise<void>;
  stop(): Promise<void>;
  getStatus(): { isReady: boolean; details: string };
}
```

### Runner Registry

`RunnerRegistry` hanterar mappning mellan steg-typer och runners:

```typescript
const STEP_RUNNER_MAPPING = {
  // Navigation
  navigate: 'playwright',
  goBack: 'playwright',
  
  // AI Processing
  extractPdfText: 'ai',
  processWithLLM: 'ai',
  
  // API (framtida)
  apiCall: 'api',
} as const;
```

### Runner Factory

`RunnerFactory` skapar runner-instanser baserat på typ:

```typescript
class RunnerFactory {
  createRunner(type: RunnerType): IRunner {
    switch (type) {
      case 'playwright':
        return new PlaywrightRunner();
      case 'ai':
        return new AIRunner();
      default:
        throw new Error(`Unknown runner type: ${type}`);
    }
  }
}
```

## Step Arkitektur

### Step-typer

Alla steg implementerar `RpaStepBase`:

```typescript
interface RpaStepBase {
  id: string;
  type: string;
  name: string;
  description?: string;
}
```

### Step Kategorier

Steg är organiserade i kategorier:

- **Navigation**: navigate, goBack, goForward, reload
- **Interaction**: click, fill, type, select, check
- **Waiting**: waitForSelector, waitForTimeout, waitForUrl
- **Extraction**: extractText, extractAttribute, takeScreenshot
- **Credentials**: fillPassword, fill2FA
- **Files**: downloadFile
- **Conditional**: ifElementExists, conditionalClick
- **AI**: extractPdfText, processWithLLM
- **API**: apiCall, apiAuth (framtida)

### Step Validering

Varje step-kategori har sin egen validator:

```typescript
// shared/src/validators/steps/navigation.ts
export function validateNavigationStep(step: NavigationStep): string[] {
  const errors: string[] = [];
  
  if (!step.url?.trim()) {
    errors.push('URL är obligatorisk');
  }
  
  return errors;
}
```

## Flow Execution Arkitektur

### Execution Flow

```mermaid
sequenceDiagram
    participant UI as Frontend
    participant API as Backend API
    participant Queue as BullMQ
    participant Executor as FlowExecutor
    participant Runner as Runner
    
    UI->>API: Start Flow
    API->>Queue: Add Job
    Queue->>Executor: Process Job
    Executor->>Runner: Execute Steps
    Runner->>Executor: Step Results
    Executor->>Queue: Update Progress
    Queue->>API: Job Complete
    API->>UI: Execution Complete
```

### Execution Context

Varje flow-execution har en context som innehåller:

```typescript
interface ExecutionContext {
  variables: Record<string, any>;
  flowId: string;
  executionId: string;
  customerId?: string;
}
```

### Retry-hantering

Systemet använder BullMQ för automatisk retry av failed executions:

**Retry-konfiguration:**
- **Attempts:** 3 totalt (1 ursprungligt + 2 retry)
- **Backoff:** Exponentiell med 2s initial delay (2s, 4s, 8s)
- **Retention:** 50 failed jobs, 100 completed jobs

**Retry-beteende:**
- **Failed executions:** Körs om från början med samma input
- **AI-steg:** Kan ge olika svar vid retry (icke-deterministiskt)
- **Cancelled executions:** Hoppas över (ingen retry)
- **Completed executions:** Hoppas över (ingen retry)

**Manuell retry:**
```typescript
// Via QueueService
await queueService.retryJob(executionId);
```

### Variable Management

Variabler skapas av steg och kan användas av efterföljande steg. Systemet använder konsistent variabelhantering mellan frontend och backend.

#### Variabelskapande Steg

Följande stegtyper skapar variabler automatiskt:

- **extractText** - Extraherad text från element
- **extractAttribute** - Extraherat attributvärde från element
- **takeScreenshot** - Base64-kodad skärmdump
- **downloadFile** - Base64-kodad fildata
- **extractPdfValues** - AI-extraherade värden från PDF
- **processWithLLM** - AI-processerat textresultat
- **apiCall** - API-svarsdata
- **apiAuth** - Autentiseringstoken
- **fortnoxCreateVoucher** - Verifikationsinformation

#### Default Variabelnamn

När användaren inte anger ett eget variabelnamn används automatiskt genererade namn:

```typescript
// Exempel på default-namn med stegindex
extractText → extractedText_1, extractedText_2, ...
takeScreenshot → screenshot_1, screenshot_2, ...
downloadFile → downloadedFile_1, downloadedFile_2, ...
extractPdfValues → extractedData_1, extractedData_2, ...
```

#### Variabeldetektering

Frontend-komponenter (VariableHelper, VariablesModal) använder samma logik som backend:

```typescript
// Konsistent mellan frontend och backend
const variableName = step.variableName || getDefaultVariableName(step.type, stepIndex);
```

Detta säkerställer att alla variabelskapande steg registreras korrekt i variabelväljaren, oavsett om de använder custom namn eller default-namn.

#### Kritisk Implementation för Step Executors

**VIKTIGT**: Alla step executors som skapar variabler MÅSTE följa denna struktur:

```typescript
export async function executeMyStep(
  step: MyStep,
  context: ExecutorContext,
  stepIndex?: number  // OBLIGATORISK för variabelkonsistens
): Promise<StepExecutionResult> {
  // Använd stepIndex för konsistent variabelnamn
  const variableName = step.variableName || getDefaultVariableName('myStep', stepIndex);
  variables[variableName] = result;

  return {
    success: true,
    variables: { [variableName]: result }
  };
}
```

**Runners måste skicka stepIndex:**

```typescript
// I Runner.executeStep()
switch (step.type) {
  case 'myStep':
    return await executeMyStep(step, context, stepIndex); // stepIndex MÅSTE skickas
}
```

Detta förhindrar inkonsistens mellan frontend och backend där frontend visar `extractedData_4` men backend skapar `extractedData`.

## Frontend Arkitektur

### Component Hierarchy

```
FlowEditor
├── FlowHeader
├── StepToolbar
│   └── StepDefinitions (per kategori)
├── FlowCanvas
│   └── StepComponents
│       └── StepEditor (per typ)
└── AIAssistant
```

### State Management

- **Local State**: React useState för komponent-specifik state
- **Context**: React Context för global state (flow data, variables)
- **Server State**: React Query för API-data caching

### Step Editor Registry

Frontend använder ett registry-system för step editors:

```typescript
export function getStepEditor(stepType: string): React.ComponentType {
  switch (stepType) {
    case 'navigate':
      return NavigationStepEditor;
    case 'click':
      return ClickStepEditor;
    // ...
    default:
      return DefaultStepEditor;
  }
}
```

## Säkerhet

### Authentication

- JWT-baserad autentisering
- Session management
- Role-based access control

### Data Protection

- Krypterade credentials
- Säker variabel-hantering
- Input validation på alla nivåer

### Browser Security

- Sandboxed browser execution
- Begränsade permissions
- Säker file handling

## Prestanda

### Optimeringar

- **Connection Pooling**: För databas och API-anrop
- **Caching**: Redis för session och temporary data
- **Lazy Loading**: Frontend komponenter laddas vid behov
- **Code Splitting**: Separata bundles för olika delar

### Monitoring

- Execution metrics
- Error tracking
- Performance monitoring
- Resource usage tracking

## Skalbarhet

### Horizontal Scaling

- Stateless backend services
- Queue-baserad job processing
- Load balancing support

### Vertical Scaling

- Efficient memory usage
- Optimized database queries
- Resource pooling

## Utvecklingsverktyg

### Build System

- TypeScript compilation
- Hot reloading för utveckling
- Optimized production builds

### Testing

- Unit tests för alla komponenter
- Integration tests för API endpoints
- E2E tests för kritiska flows

### Documentation

- Auto-generated API docs
- Component documentation
- Architecture decision records

## Deployment

### Miljöer

- **Development**: Local development med hot reload
- **Staging**: Test environment med production-like setup
- **Production**: Optimized för prestanda och säkerhet

### CI/CD Pipeline

1. Code commit
2. Automated tests
3. Build artifacts
4. Deploy to staging
5. Integration tests
6. Deploy to production
7. Health checks

## Framtida Utveckling

### Planerade Förbättringar

- **Plugin System**: Dynamisk loading av runners
- **Visual Flow Builder**: Drag-and-drop interface
- **Advanced Scheduling**: Cron-based scheduling
- **Monitoring Dashboard**: Real-time execution monitoring
- **API Runner**: REST/GraphQL API integration
- **Database Runner**: Direct database operations
