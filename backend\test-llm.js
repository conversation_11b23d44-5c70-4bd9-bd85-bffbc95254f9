// Test script för LLM provider-arkitekturen
// Set test environment variables
process.env.LLM_PROVIDER = 'openai';
process.env.LLM_DEFAULT_MODEL = 'gpt-4o-mini';
process.env.OPENAI_API_KEY = 'test-key'; // Mock key for testing

const { LLMService } = require('./dist/services/llm/LLMService');
const { LLMProviderFactory } = require('./dist/services/llm/LLMProviderFactory');

async function testLLMService() {
  console.log('🧪 Testing LLM Service...');

  try {
    // Test 1: Health check
    console.log('\n1. Testing connection...');
    const healthCheck = await LLMService.testConnection();
    console.log('Health check result:', healthCheck);

    if (!healthCheck.success) {
      console.log('❌ LLM provider not configured properly');
      console.log('Error:', healthCheck.error);

      // Test provider factory
      console.log('\n2. Testing provider factory...');
      console.log('Default model:', LLMProviderFactory.getDefaultModel());
      console.log('Fallback model:', LLMProviderFactory.getFallbackModel());

      // Test model switching
      console.log('\n3. Testing model switching...');
      process.env.LLM_DEFAULT_MODEL = 'o1-mini';
      LLMProviderFactory.reset(); // Reset to pick up new config
      console.log('New default model:', LLMProviderFactory.getDefaultModel());

      // Test provider switching
      console.log('\n4. Testing provider switching...');
      process.env.LLM_PROVIDER = 'azure';
      LLMProviderFactory.reset();
      try {
        const healthCheck2 = await LLMService.testConnection();
        console.log('Azure provider result:', healthCheck2);
      } catch (error) {
        console.log('Azure provider error (expected):', error.message);
      }

      return;
    }
    
    // Test 2: Simple chat completion
    console.log('\n2. Testing chat completion...');
    const response = await LLMService.createChatCompletion([
      { role: 'user', content: 'Säg hej på svenska' }
    ], {
      maxTokens: 50
    });
    
    console.log('Response:', response.content);
    console.log('Usage:', response.usage);
    
    // Test 3: Get available models
    console.log('\n3. Testing available models...');
    const models = LLMService.getAvailableModels();
    console.log('Available models:', models);
    
    console.log('\n✅ All tests passed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testLLMService();
