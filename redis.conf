# Redis Production Configuration

# Persistence
appendonly yes
appendfsync everysec
save 900 1
save 300 10
save 60 10000

# Memory management
maxmemory 512mb
maxmemory-policy allkeys-lru

# Security
bind 0.0.0.0
protected-mode yes
# requirepass your-redis-password-here  # Uncomment and set password

# Logging
loglevel notice
logfile ""

# Performance
tcp-keepalive 300
timeout 0

# Disable dangerous commands in production
rename-command FLUSHD<PERSON> ""
rename-command FLUS<PERSON>LL ""
rename-command DEBUG ""
rename-command CONF<PERSON> ""
