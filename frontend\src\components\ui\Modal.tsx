import { ReactNode, useEffect } from 'react'
import { Portal } from './Portal'

interface ModalProps {
  isOpen: boolean
  onClose: () => void
  title: string
  children: ReactNode
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  showCloseButton?: boolean
}

export function Modal({ isOpen, onClose, title, children, size = 'lg', showCloseButton = true }: ModalProps) {
  // Close modal on Escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, onClose])

  if (!isOpen) return null

  const sizeStyles = {
    sm: { maxWidth: '28rem' }, // ~448px
    md: { maxWidth: '32rem' }, // ~512px
    lg: { maxWidth: '50vw' },  // 50% of viewport width
    xl: { maxWidth: '70vw' },  // 70% of viewport width
    full: { maxWidth: '90vw' } // 90% of viewport width
  }

  return (
    <Portal>
      <div
        className="modal-overlay"
        onClick={onClose}
      >
        <div
          className="modal-content"
          style={{
            width: '100%',
            ...sizeStyles[size]
          }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Modal Header */}
          <div className="dashboard-header" style={{
            flexShrink: 0,
            borderBottom: '1px solid #e5e7eb',
            margin: 0
          }}>
            <div className="dashboard-header-content">
              <p className="dashboard-title" style={{ fontSize: '1.5rem' }}>
                {title}
              </p>
            </div>
            {showCloseButton && (
              <button
                onClick={onClose}
                className="action-button secondary"
              >
                <span>✕ Stäng</span>
              </button>
            )}
          </div>

          {/* Modal Content */}
          <div style={{
            flex: 1,
            overflow: 'auto',
            padding: 0
          }}>
            {children}
          </div>
        </div>
      </div>
    </Portal>
  )
}
