#!/usr/bin/env node

/**
 * RPA Step Generator
 * 
 * Automatiskt genererar alla nödvändiga filer för ett nytt RPA-steg
 * baserat på templates.
 * 
 * Användning:
 * node tools/generate-step.js --type=myNewStep --category=interaction --runner=playwright
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Färger för console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✓ ${message}`, 'green');
}

function logError(message) {
  log(`✗ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠ ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ ${message}`, 'blue');
}

// Parse command line arguments
function parseArgs() {
  const args = process.argv.slice(2);
  const options = {};

  args.forEach(arg => {
    if (arg.startsWith('--')) {
      const [key, value] = arg.substring(2).split('=');
      options[key] = value;
    }
  });

  return options;
}

// Validate arguments
function validateArgs(options) {
  const required = ['type', 'category', 'runner'];
  const validCategories = ['navigation', 'interaction', 'waiting', 'extraction', 'credentials', 'files', 'conditional', 'ai', 'api'];
  const validRunners = ['playwright', 'ai', 'api'];

  for (const field of required) {
    if (!options[field]) {
      logError(`Obligatoriskt argument saknas: --${field}`);
      return false;
    }
  }

  if (!validCategories.includes(options.category)) {
    logError(`Ogiltig kategori: ${options.category}. Giltiga: ${validCategories.join(', ')}`);
    return false;
  }

  if (!validRunners.includes(options.runner)) {
    logError(`Ogiltig runner: ${options.runner}. Giltiga: ${validRunners.join(', ')}`);
    return false;
  }

  // Validate step type format
  if (!/^[a-z][a-zA-Z0-9]*$/.test(options.type)) {
    logError('Step-typ måste vara camelCase och börja med liten bokstav');
    return false;
  }

  return true;
}

// Generate different case variations of step name
function generateCaseVariations(stepType) {
  return {
    camelCase: stepType,
    pascalCase: stepType.charAt(0).toUpperCase() + stepType.slice(1),
    kebabCase: stepType.replace(/([A-Z])/g, '-$1').toLowerCase(),
    upperCase: stepType.replace(/([A-Z])/g, '_$1').toUpperCase()
  };
}

// Read template file and replace variables
function processTemplate(templatePath, variables) {
  if (!fs.existsSync(templatePath)) {
    throw new Error(`Template inte hittad: ${templatePath}`);
  }

  let content = fs.readFileSync(templatePath, 'utf8');

  // Replace all template variables
  Object.entries(variables).forEach(([key, value]) => {
    const regex = new RegExp(`{{${key}}}`, 'g');
    content = content.replace(regex, value);
  });

  return content;
}

// Ensure directory exists
function ensureDir(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    logInfo(`Skapade mapp: ${dirPath}`);
  }
}

// Write file with content
function writeFile(filePath, content) {
  ensureDir(path.dirname(filePath));
  fs.writeFileSync(filePath, content, 'utf8');
  logSuccess(`Skapade fil: ${filePath}`);
}

// Check if file exists and ask for confirmation
function checkFileExists(filePath) {
  if (fs.existsSync(filePath)) {
    logWarning(`Fil existerar redan: ${filePath}`);
    // I en riktig implementation skulle vi fråga användaren
    // För nu hoppar vi över befintliga filer
    return true;
  }
  return false;
}

// Generate step type definition
function generateStepType(options, variables) {
  const templatePath = path.join(__dirname, '../docs/templates/new-step/step-type.ts.template');
  const outputPath = path.join(__dirname, `../shared/src/types/steps/${options.category}.ts`);

  if (checkFileExists(outputPath)) {
    logInfo(`Hoppar över step-typ (fil existerar): ${outputPath}`);
    logInfo(`Lägg till manuellt i ${outputPath}:`);
    log(`
export interface ${variables.PASCAL_CASE}Step extends RpaStepBase {
  type: '${variables.STEP_TYPE}';
  // Lägg till step-specifika properties här
}`, 'yellow');
    return;
  }

  const content = processTemplate(templatePath, variables);
  writeFile(outputPath, content);
}

// Generate validator
function generateValidator(options, variables) {
  const templatePath = path.join(__dirname, '../docs/templates/new-step/validator.ts.template');
  const outputPath = path.join(__dirname, `../shared/src/validators/steps/${options.category}.ts`);

  if (checkFileExists(outputPath)) {
    logInfo(`Hoppar över validator (fil existerar): ${outputPath}`);
    logInfo(`Lägg till manuellt i ${outputPath}:`);
    log(`
export function validate${variables.PASCAL_CASE}Step(step: ${variables.PASCAL_CASE}Step): string[] {
  // Implementera validering
}

export function create${variables.PASCAL_CASE}Step(): ${variables.PASCAL_CASE}Step {
  // Implementera factory
}`, 'yellow');
    return;
  }

  const content = processTemplate(templatePath, variables);
  writeFile(outputPath, content);
}

// Generate runner method
function generateRunnerMethod(options, variables) {
  const templatePath = path.join(__dirname, '../docs/templates/new-step/runner-method.ts.template');
  const runnerPath = options.runner === 'playwright' ? 'playwright/PlaywrightRunner.ts' :
                     options.runner === 'ai' ? 'ai/AIRunner.ts' :
                     `${options.runner}/${variables.PASCAL_CASE}Runner.ts`;
  
  const outputPath = path.join(__dirname, `../backend/src/runners/${runnerPath}`);

  if (checkFileExists(outputPath)) {
    logInfo(`Hoppar över runner-metod (fil existerar): ${outputPath}`);
    logInfo(`Lägg till manuellt i ${outputPath}:`);
    log(`
async execute${variables.PASCAL_CASE}Step(
  step: ${variables.PASCAL_CASE}Step, 
  context: ExecutionContext
): Promise<void> {
  // Implementera step-logik
}

// Lägg till i executeStep switch:
case '${variables.STEP_TYPE}':
  await this.execute${variables.PASCAL_CASE}Step(step as ${variables.PASCAL_CASE}Step, context);
  break;`, 'yellow');
    return;
  }

  const content = processTemplate(templatePath, variables);
  writeFile(outputPath, content);
}

// Generate editor component
function generateEditor(options, variables) {
  const templatePath = path.join(__dirname, '../docs/templates/new-step/editor.tsx.template');
  const outputDir = path.join(__dirname, `../frontend/src/components/flow-editor/step-editors/${options.category}`);
  const outputPath = path.join(outputDir, `${variables.PASCAL_CASE}StepEditor.tsx`);

  if (checkFileExists(outputPath)) {
    logInfo(`Hoppar över editor (fil existerar): ${outputPath}`);
    return;
  }

  const content = processTemplate(templatePath, variables);
  writeFile(outputPath, content);
}

// Generate toolbar entry
function generateToolbarEntry(options, variables) {
  const templatePath = path.join(__dirname, '../docs/templates/new-step/toolbar-entry.ts.template');
  const outputPath = path.join(__dirname, `../frontend/src/components/flow-editor/step-definitions/${options.category}.ts`);

  if (checkFileExists(outputPath)) {
    logInfo(`Hoppar över toolbar-entry (fil existerar): ${outputPath}`);
    logInfo(`Lägg till manuellt i ${outputPath}:`);
    log(`
export const ${variables.STEP_TYPE}Definition: StepDefinition = {
  type: '${variables.STEP_TYPE}',
  name: '${variables.STEP_DESCRIPTION}',
  category: '${options.category}',
  // ... resten av definitionen
};`, 'yellow');
    return;
  }

  const content = processTemplate(templatePath, variables);
  writeFile(outputPath, content);
}

// Update registry files
function updateRegistries(options, variables) {
  logInfo('Uppdaterar registry-filer...');
  
  // Update step-runner mapping
  const mappingPath = path.join(__dirname, '../backend/src/runners/registry/stepTypes.ts');
  if (fs.existsSync(mappingPath)) {
    logInfo(`Lägg till manuellt i ${mappingPath}:`);
    log(`  ${variables.STEP_TYPE}: '${options.runner}',`, 'yellow');
  }

  // Update step editor registry
  const editorRegistryPath = path.join(__dirname, '../frontend/src/components/flow-editor/step-editors/StepEditorRegistry.tsx');
  if (fs.existsSync(editorRegistryPath)) {
    logInfo(`Lägg till manuellt i ${editorRegistryPath}:`);
    log(`case '${variables.STEP_TYPE}':
  return <${variables.PASCAL_CASE}StepEditor {...props} step={step as ${variables.PASCAL_CASE}Step} />;`, 'yellow');
  }

  // Update step definitions index
  const definitionsIndexPath = path.join(__dirname, '../frontend/src/components/flow-editor/step-definitions/index.ts');
  if (fs.existsSync(definitionsIndexPath)) {
    logInfo(`Lägg till manuellt i ${definitionsIndexPath}:`);
    log(`import { ${variables.STEP_TYPE}Definition } from './${options.category}';
// Lägg till i stepDefinitions array: ${variables.STEP_TYPE}Definition,`, 'yellow');
  }
}

// Run TypeScript compilation to check for errors
function checkCompilation() {
  logInfo('Kontrollerar TypeScript-kompilering...');
  
  try {
    execSync('npm run type-check', { stdio: 'pipe' });
    logSuccess('TypeScript-kompilering lyckades');
  } catch (error) {
    logWarning('TypeScript-kompileringsfel upptäckta');
    logInfo('Kör "npm run type-check" för att se detaljer');
  }
}

// Main function
function main() {
  log(`${colors.bold}${colors.blue}RPA Step Generator${colors.reset}\n`);

  const options = parseArgs();

  if (!validateArgs(options)) {
    logError('\nAnvändning:');
    log('node tools/generate-step.js --type=myNewStep --category=interaction --runner=playwright\n');
    log('Kategorier: navigation, interaction, waiting, extraction, credentials, files, conditional, ai, api');
    log('Runners: playwright, ai, api');
    process.exit(1);
  }

  const caseVariations = generateCaseVariations(options.type);
  
  // Template variables
  const variables = {
    STEP_TYPE: options.type,
    STEP_CATEGORY: options.category,
    RUNNER_TYPE: options.runner,
    STEP_DESCRIPTION: `${caseVariations.pascalCase} Steg`,
    PASCAL_CASE: caseVariations.pascalCase,
    KEBAB_CASE: caseVariations.kebabCase,
    UPPER_CASE: caseVariations.upperCase
  };

  logInfo(`Genererar steg: ${options.type}`);
  logInfo(`Kategori: ${options.category}`);
  logInfo(`Runner: ${options.runner}\n`);

  try {
    // Generate all files
    generateStepType(options, variables);
    generateValidator(options, variables);
    generateRunnerMethod(options, variables);
    generateEditor(options, variables);
    generateToolbarEntry(options, variables);
    
    // Update registries
    updateRegistries(options, variables);
    
    // Check compilation
    checkCompilation();

    log(`\n${colors.bold}${colors.green}✓ Step-generering slutförd!${colors.reset}\n`);
    
    logInfo('Nästa steg:');
    log('1. Implementera step-specifik logik i genererade filer');
    log('2. Uppdatera union types och registries manuellt');
    log('3. Testa steget i flow-editorn');
    log('4. Skriv enhetstester');
    log('5. Uppdatera dokumentation\n');

  } catch (error) {
    logError(`Fel vid generering: ${error.message}`);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  generateCaseVariations,
  processTemplate,
  validateArgs
};
