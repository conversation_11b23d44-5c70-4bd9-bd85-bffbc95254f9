import React, { useState, useEffect } from 'react'
import { FlowSchedule, RpaFlow } from '@rpa-project/shared'
import { scheduleApi, flowApi } from '../services/api'

interface ScheduleListProps {
  flowId?: string
  onScheduleSelect?: (schedule: FlowSchedule) => void
  onScheduleEdit?: (schedule: FlowSchedule) => void
  onScheduleDelete?: (scheduleId: string) => void
}

export const ScheduleList: React.FC<ScheduleListProps> = ({
  flowId,
  onScheduleSelect: _,
  onScheduleEdit,
  onScheduleDelete
}) => {
  const [schedules, setSchedules] = useState<FlowSchedule[]>([])
  const [flows, setFlows] = useState<RpaFlow[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadSchedules()
    if (!flowId) {
      loadFlows()
    }
  }, [flowId])

  const loadSchedules = async () => {
    try {
      setLoading(true)
      const response = flowId 
        ? await scheduleApi.getSchedulesByFlowId(flowId)
        : await scheduleApi.getSchedules()
      
      if (response.success) {
        setSchedules(response.data || [])
      } else {
        setError(response.error || 'Failed to load schedules')
      }
    } catch (err) {
      setError('Failed to load schedules')
      console.error('Error loading schedules:', err)
    } finally {
      setLoading(false)
    }
  }

  const loadFlows = async () => {
    try {
      const response = await flowApi.getFlows()
      if (response.success) {
        setFlows(response.data || [])
      }
    } catch (err) {
      console.error('Error loading flows:', err)
    }
  }

  const handleToggleSchedule = async (schedule: FlowSchedule) => {
    try {
      const response = await scheduleApi.toggleSchedule(schedule.id)
      if (response.success) {
        setSchedules(schedules.map(s => 
          s.id === schedule.id ? response.data! : s
        ))
      } else {
        setError(response.error || 'Failed to toggle schedule')
      }
    } catch (err) {
      setError('Failed to toggle schedule')
      console.error('Error toggling schedule:', err)
    }
  }

  const handleDeleteSchedule = async (schedule: FlowSchedule) => {
    if (!confirm(`Är du säker på att du vill ta bort schemat "${schedule.name}"?`)) {
      return
    }

    try {
      const response = await scheduleApi.deleteSchedule(schedule.id)
      if (response.success) {
        setSchedules(schedules.filter(s => s.id !== schedule.id))
        onScheduleDelete?.(schedule.id)
      } else {
        setError(response.error || 'Failed to delete schedule')
      }
    } catch (err) {
      setError('Failed to delete schedule')
      console.error('Error deleting schedule:', err)
    }
  }

  const getFlowName = (flowId: string): string => {
    const flow = flows.find(f => f.id === flowId)
    return flow?.name || flowId
  }

  const formatNextRun = (nextRunAt?: Date): string => {
    if (!nextRunAt) return 'Inte schemalagd'

    const date = new Date(nextRunAt)
    const now = new Date()
    const diffMs = date.getTime() - now.getTime()

    if (diffMs < 0) return 'Försenad'

    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffHours / 24)

    if (diffDays > 0) {
      return `Om ${diffDays} dag${diffDays > 1 ? 'ar' : ''}`
    } else if (diffHours > 0) {
      return `Om ${diffHours} timm${diffHours > 1 ? 'ar' : 'e'}`
    } else {
      const diffMinutes = Math.floor(diffMs / (1000 * 60))
      return `Om ${diffMinutes} minut${diffMinutes > 1 ? 'er' : ''}`
    }
  }

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-text">Laddar scheman...</div>
      </div>
    )
  }



  if (error) {
    return (
      <div className="error-card">
        <h3 className="error-title">Fel vid laddning av scheman</h3>
        <p className="error-message">{error}</p>
        <button onClick={loadSchedules} className="action-button primary">
          <span>Försök igen</span>
        </button>
      </div>
    )
  }

  const getScheduleStats = () => {
    const total = schedules.length
    const enabled = schedules.filter(s => s.enabled).length
    const disabled = schedules.filter(s => !s.enabled).length
    const upcoming = schedules.filter(s => {
      if (!s.nextRunAt || !s.enabled) return false
      const nextRun = new Date(s.nextRunAt)
      const now = new Date()
      const diffHours = (nextRun.getTime() - now.getTime()) / (1000 * 60 * 60)
      return diffHours > 0 && diffHours <= 24
    }).length

    return { total, enabled, disabled, upcoming }
  }

  const stats = getScheduleStats()

  return (
    <div>
      {/* Stats Cards */}
      {schedules.length > 0 && (
        <div className="stats-grid">
          <div className="stat-card">
            <p className="stat-label">Totalt antal scheman</p>
            <p className="stat-value">{stats.total}</p>
            <p className="stat-change">+0%</p>
          </div>
          <div className="stat-card">
            <p className="stat-label">Aktiva scheman</p>
            <p className="stat-value">{stats.enabled}</p>
            <p className="stat-change">+0%</p>
          </div>
          <div className="stat-card">
            <p className="stat-label">Inaktiva scheman</p>
            <p className="stat-value">{stats.disabled}</p>
            <p className="stat-change">+0%</p>
          </div>
          <div className="stat-card">
            <p className="stat-label">Kommande (24h)</p>
            <p className="stat-value">{stats.upcoming}</p>
            <p className="stat-change">+0%</p>
          </div>
        </div>
      )}

      {/* Schedules List */}
      {schedules.length === 0 ? (
        <div className="empty-state-container">
          <div className="empty-state">
            <div className="empty-state-icon">⏰</div>
            <p className="empty-state-title">
              {flowId ? 'Inga scheman hittades för detta flöde' : 'Inga scheman hittades'}
            </p>
            <p className="empty-state-subtitle">
              {flowId ? 'Skapa ett schema för att automatisera detta flöde' : 'Skapa ditt första schema för att automatisera flödeskörningar'}
            </p>
          </div>
        </div>
      ) : (
        <>
          <h2 className="section-title">Dina scheman</h2>
          <div className="table-container">
            <div className="activity-table">
              <table className="table">
                <thead>
                  <tr>
                    <th>Schemanamn</th>
                    <th>Status</th>
                    <th>Flöde</th>
                    <th>Nästa körning</th>
                    <th>Åtgärder</th>
                  </tr>
                </thead>
                <tbody>
                  {schedules.map(schedule => (
                    <tr key={schedule.id}>
                      <td>
                        <div>
                          <div className="flow-name">{schedule.name}</div>
                          {schedule.description && (
                            <div className="flow-description-small">{schedule.description}</div>
                          )}
                        </div>
                      </td>
                      <td>
                        <button className={`status-button ${schedule.enabled ? 'status-completed' : 'status-cancelled'}`}>
                          <span>{schedule.enabled ? 'Aktiv' : 'Inaktiv'}</span>
                        </button>
                      </td>
                      <td className="secondary-text">
                        {!flowId ? getFlowName(schedule.flowId) : 'Aktuellt flöde'}
                      </td>
                      <td className="secondary-text">
                        {formatNextRun(schedule.nextRunAt)}
                      </td>
                      <td>
                        <div className="flow-actions">
                          <button
                            onClick={() => handleToggleSchedule(schedule)}
                            className={`action-button-small ${schedule.enabled ? 'icon-only' : 'icon-only'}`}
                            title={schedule.enabled ? 'Inaktivera' : 'Aktivera'}
                          >
                            {schedule.enabled ? (
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <rect x="6" y="4" width="4" height="16"></rect>
                                <rect x="14" y="4" width="4" height="16"></rect>
                              </svg>
                            ) : (
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <polygon points="5,3 19,12 5,21"></polygon>
                              </svg>
                            )}
                          </button>
                          <button
                            onClick={() => onScheduleEdit?.(schedule)}
                            className="action-button-small icon-only"
                            title="Redigera schema"
                          >
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                              <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                            </svg>
                          </button>
                          <button
                            onClick={() => handleDeleteSchedule(schedule)}
                            className="action-button-small danger"
                            title="Ta bort schema"
                          >
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <polyline points="3,6 5,6 21,6"></polyline>
                              <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                              <line x1="10" y1="11" x2="10" y2="17"></line>
                              <line x1="14" y1="11" x2="14" y2="17"></line>
                            </svg>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </>
      )}
    </div>
  )
}

export default ScheduleList
