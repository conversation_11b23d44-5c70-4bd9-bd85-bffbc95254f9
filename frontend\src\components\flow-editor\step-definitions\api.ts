import { StepDefinition, StepCategory, STEP_CATEGORIES } from './categories'

export const apiSteps: StepDefinition[] = [
  {
    type: 'fortnoxCreateVoucher',
    name: 'Skapa Fortnox Verifikation',
    icon: '🧾',
    description: 'Ska<PERSON> en verifikation i Fortnox med kontomappningar'
  },
  {
    type: 'fortnoxUploadFile',
    name: 'Ladda upp fil till Fortnox',
    icon: '📤',
    description: 'Ladda upp en fil till Fortnox arkiv'
  },
  {
    type: 'fortnoxAttachFileToVoucher',
    name: 'Koppla fil till verifikation',
    icon: '📎',
    description: 'Koppla en uppladdad fil till en befintlig verifikation'
  },
  {
    type: 'fortnoxUploadAndCreateVoucher',
    name: 'Ladda upp fil och skapa verifikation',
    icon: '📋',
    description: 'Ladda upp fil och skapa verifikation med automatisk koppling'
  },
  // eAccounting API steps
  {
    type: 'eAccountingUploadFile',
    name: 'Ladda upp fil till eAccounting',
    icon: '📤',
    description: 'Ladda upp en fil till eAccounting Attachments'
  },
  {
    type: 'eAccountingCreateVoucher',
    name: 'Skapa eAccounting Verifikation',
    icon: '🧾',
    description: 'Skapa en verifikation i eAccounting med AI-assistans'
  },
  {
    type: 'eAccountingAttachFileToVoucher',
    name: 'Koppla fil till eAccounting verifikation',
    icon: '📎',
    description: 'Koppla en uppladdad fil till en befintlig verifikation i eAccounting'
  },
  {
    type: 'eAccountingUploadAndCreateVoucher',
    name: 'Ladda upp fil och skapa eAccounting verifikation',
    icon: '📋',
    description: 'Ladda upp fil och skapa verifikation med automatisk koppling i eAccounting'
  }
]

export const apiCategory: StepCategory = {
  name: STEP_CATEGORIES.API_INTEGRATION,
  icon: '🌐',
  steps: apiSteps
}
