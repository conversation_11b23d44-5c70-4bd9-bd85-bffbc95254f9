import { LLMProviderFactory } from './LLMProviderFactory';
import { ChatCompletionRequest, ChatCompletionResponse, ChatMessage } from './interfaces';
import { ModelRegistry } from './ModelRegistry';

export class LLMService {
  static async createChatCompletion(
    messages: ChatMessage[],
    options: {
      model?: string;
      temperature?: number;
      maxTokens?: number;
    } = {}
  ): Promise<ChatCompletionResponse> {
    const provider = LLMProviderFactory.getInstance();

    if (!provider.isConfigured()) {
      throw new Error(`LLM provider ${provider.name} is not properly configured`);
    }

    const model = options.model || LLMProviderFactory.getDefaultModel();

    // Validera att modellen stöds av providern
    const supportedModels = provider.getSupportedModels();
    if (!supportedModels.includes(model)) {
      console.warn(`Model ${model} not supported by ${provider.name}, using fallback`);
      const fallbackModel = LLMProviderFactory.getFallbackModel();
      if (!supportedModels.includes(fallbackModel)) {
        throw new Error(`Neither default model ${model} nor fallback model ${fallbackModel} is supported by ${provider.name}`);
      }
      options.model = fallbackModel;
    }

    const request: ChatCompletionRequest = {
      model,
      messages,
      temperature: options.temperature,
      maxTokens: options.maxTokens
    };

    try {
      return await provider.createChatCompletion(request);
    } catch (error) {
      console.error(`LLM request failed with ${provider.name}:`, error);
      throw error;
    }
  }

  static async testConnection(): Promise<{ success: boolean; provider: string; error?: string }> {
    try {
      const provider = LLMProviderFactory.getInstance();
      const success = await provider.testConnection();
      return { success, provider: provider.name };
    } catch (error) {
      const provider = LLMProviderFactory.getInstance();
      return {
        success: false,
        provider: provider.name,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  static getAvailableModels(): Array<{ id: string; displayName: string; provider: string }> {
    const provider = LLMProviderFactory.getInstance();
    const supportedModelIds = provider.getSupportedModels();

    return ModelRegistry.getAllModels()
      .filter(model =>
        model.provider === provider.name &&
        supportedModelIds.includes(model.id)
      )
      .map(model => ({
        id: model.id,
        displayName: model.displayName,
        provider: model.provider
      }));
  }
}
