# Fortnox Filhantering - Exempel-flöden

Detta dokument beskriver de kompletta exempel-flöden som finns tillgängliga för Fortnox filhantering.

## Tillgängliga Exempel

### 1. Automatisk Fakturahantering - Komplett
**Fil:** `example-flows.json` → `flows[0]`

**Beskrivning:** Ett komplett flöde som automatiserar hela processen från att ladda ner en faktura från en leverantörsportal till att skapa en verifikation i Fortnox med bifogad fil.

**Steg:**
1. Navigera till leverantörsportal
2. Logga in med sparade credentials
3. Extrahera fakturadata (nummer, leverantör, belopp, datum)
4. Ladda ner faktura-PDF
5. Ladda upp fil och skapa verifikation i en operation
6. Ta skärmdump av resultat

**Användningsfall:**
- Automatisk fakturahantering
- Integration med leverantörsportaler
- <PERSON><PERSON><PERSON> bokföringsprocess

**Viktiga funktioner:**
- Anv<PERSON><PERSON> `fortnoxUploadAndCreateVoucher` för effektivitet
- Dynamiska filnamn med variabelinterpolation
- AI-driven verifikationsskapande med specifika kontomappningar

### 2. Kvittohantering - Separata Steg
**Fil:** `example-flows.json` → `flows[1]`

**Beskrivning:** Visar hur man hanterar utgiftskvitton med separata steg för filuppladdning och verifikationsskapande, vilket ger mer kontroll över processen.

**Steg:**
1. Navigera till utgiftssystem
2. Ladda ner kvitto
3. Extrahera utgiftsdata
4. Ladda upp kvitto till Fortnox (`fortnoxUploadFile`)
5. Skapa verifikation med bifogad fil (`fortnoxCreateVoucher`)

**Användningsfall:**
- Utgiftshantering
- Kvittoarkivering
- Separerad fil- och verifikationshantering

**Viktiga funktioner:**
- Separata steg för bättre kontroll
- Användning av kassaserie (C) för utgifter
- Flexibel kontomappning baserat på utgiftskategori

### 3. Bankutdrag med Filkoppling
**Fil:** `example-flows.json` → `flows[2]`

**Beskrivning:** Demonstrerar hur man laddar ner bankutdrag och kopplar dem till befintliga verifikationer.

**Steg:**
1. Logga in på nätbank
2. Ladda ner bankutdrag
3. Ladda upp till Fortnox arkiv
4. Koppla till befintlig verifikation (`fortnoxAttachFileToVoucher`)

**Användningsfall:**
- Bankrekonciliation
- Dokumentation av banktransaktioner
- Koppling av underlag till befintliga verifikationer

**Viktiga funktioner:**
- Använder `fortnoxAttachFileToVoucher` för att koppla till befintliga verifikationer
- Bankintegration
- Filarkivering utan ny verifikation

### 4. PDF-extraktion med Verifikation
**Fil:** `example-flows.json` → `flows[3]`

**Beskrivning:** Avancerat flöde som kombinerar PDF-extraktion med automatisk verifikationsskapande.

**Steg:**
1. Ladda ner faktura-PDF
2. Extrahera strukturerad data med `extractPdfValues`
3. Skapa verifikation baserat på extraherad data

**Användningsfall:**
- Automatisk dataextraktion från PDF-fakturor
- AI-driven bokföring baserat på extraherad data
- Komplex fakturahantering

**Viktiga funktioner:**
- Kombinerar `extractPdfValues` med Fortnox-integration
- AI-analys av extraherad data
- Automatisk kontomappning baserat på innehåll

## Mallar och Återanvändbara Komponenter

### AI-prompter
Filen innehåller vanliga AI-prompter för olika verifikationstyper:

```json
{
  "inköpsfaktura_med_moms": "Skapa en inköpsverifikation med 25% moms...",
  "utgiftskvitto": "Skapa en utgiftsverifikation...",
  "bankverifikation": "Skapa en bankverifikation...",
  "försäljningsfaktura": "Skapa en försäljningsverifikation..."
}
```

### Kontomappningar
Standardkontomappningar enligt BAS-kontoplanen:

```json
{
  "inköp": "4010",
  "försäljning": "3010",
  "ingående_moms": "2640",
  "utgående_moms": "2610",
  "leverantörsskuld": "2440",
  "kundfordringar": "1510",
  "bank": "1930",
  "allmänna_kostnader": "6250"
}
```

## Hur man använder exemplen

### 1. Importera exempel-flöde
```bash
# Kopiera JSON-innehållet från example-flows.json
# Klistra in i RPA-applikationens flödeseditor
```

### 2. Anpassa för din miljö
- Uppdatera URL:er till dina system
- Konfigurera credentials för inloggning
- Anpassa selektorer för dina webbsidor
- Modifiera kontomappningar enligt din kontoplan

### 3. Testa steg för steg
- Kör enskilda steg för att verifiera funktionalitet
- Kontrollera att variabler sätts korrekt
- Validera Fortnox-integration

### 4. Produktionsdriftsättning
- Konfigurera schemaläggning
- Sätt upp felhantering och notifieringar
- Övervaka loggfiler

## Best Practices från Exemplen

### 1. Variabelhantering
```json
{
  "variableName": "var-invoice-pdf",
  "filename": "faktura-${var-supplier-name}-${var-invoice-number}.pdf"
}
```
- Använd beskrivande variabelnamn
- Interpolera variabler i filnamn för bättre organisation

### 2. Timeout-hantering
```json
{
  "timeout": 120000,  // 2 minuter för komplexa operationer
  "timeout": 60000,   // 1 minut för filuppladdning
  "timeout": 30000    // 30 sekunder för vanliga operationer
}
```

### 3. AI-prompter
```json
{
  "aiPrompt": "Skapa en inköpsverifikation för faktura ${var-invoice-number} från ${var-supplier-name}. Totalbelopp: ${var-total-amount} SEK (inklusive 25% moms). Använd konto 4010 för inköp av varor/tjänster, konto 2640 för ingående moms (25%) och konto 2440 för leverantörsskuld."
}
```
- Var specifik med kontomappningar
- Inkludera variabelreferenser
- Specificera momssatser och beräkningar

### 4. Felhantering
```json
{
  "description": "Ladda upp faktura och skapa verifikation i Fortnox",
  "timeout": 120000
}
```
- Använd beskrivande beskrivningar för felsökning
- Sätt lämpliga timeouts för olika operationer
- Inkludera skärmdumpar för verifiering

## Anpassning för Olika Branscher

### E-handel
- Fokus på försäljningsverifikationer
- Automatisk hantering av returer
- Integration med betalningslösningar

### Konsultverksamhet
- Tidsrapportering och fakturering
- Utgiftshantering för resor
- Projektbaserad bokföring

### Tillverkning
- Materialinköp och lagerhantering
- Produktionskostnader
- Leverantörsfakturor

### Tjänsteföretag
- Abonnemangsfakturering
- Personalkostnader
- Kontorskostnader

## Felsökning

### Vanliga Problem
1. **Selektorer fungerar inte** - Uppdatera CSS-selektorer för din webbsida
2. **Credentials saknas** - Konfigurera inloggningsuppgifter i systemet
3. **Fortnox API-fel** - Kontrollera OAuth2-token och behörigheter
4. **PDF-extraktion misslyckas** - Kontrollera PDF-kvalitet och format

### Debug-tips
- Använd `takeScreenshot` för att verifiera steg
- Kontrollera variabelvärden i loggarna
- Testa Fortnox-steg separat först
- Validera AI-prompter med testdata

## Support och Vidareutveckling

För frågor om exemplen eller förslag på nya flöden:
1. Kontrollera dokumentationen i `docs/examples/fortnox-file-handling/`
2. Granska teknisk dokumentation i `TECHNICAL.md`
3. Testa med enkla exempel först
4. Bygg upp komplexitet stegvis
