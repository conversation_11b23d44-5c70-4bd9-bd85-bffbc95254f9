import { spawn, ChildProcess } from 'child_process';
import { promisify } from 'util';
import { exec } from 'child_process';

const execAsync = promisify(exec);

export interface VNCServiceStatus {
  isRunning: boolean;
  xvfbPid?: number;
  vncPid?: number;
  noVncPid?: number;
  port?: number;
  webPort?: number;
}

/**
 * Service for managing VNC and noVNC processes dynamically
 * Only starts VNC services when needed for non-headless manual executions
 */
export class VNCService {
  private static instance: VNCService;
  private xvfbProcess: ChildProcess | null = null;
  private vncProcess: ChildProcess | null = null;
  private noVncProcess: ChildProcess | null = null;
  private isRunning = false;
  private activeExecutions = new Set<string>();

  private constructor() {}

  static getInstance(): VNCService {
    if (!VNCService.instance) {
      VNCService.instance = new VNCService();
    }
    return VNCService.instance;
  }

  /**
   * Start VNC services for a specific execution
   */
  async startVNCForExecution(executionId: string): Promise<{ vncUrl: string; webUrl: string }> {
    console.log(`🖥️ Starting VNC services for execution: ${executionId}`);
    
    this.activeExecutions.add(executionId);

    if (!this.isRunning) {
      await this.startVNCServices();
    }

    return {
      vncUrl: 'vnc://localhost:5900',
      webUrl: 'http://localhost:6080/vnc.html?host=localhost&port=6080&autoconnect=true&resize=off'
    };
  }

  /**
   * Stop VNC services for a specific execution
   */
  async stopVNCForExecution(executionId: string): Promise<void> {
    console.log(`🛑 Stopping VNC services for execution: ${executionId}`);
    
    this.activeExecutions.delete(executionId);

    // If no more active executions, stop VNC services
    if (this.activeExecutions.size === 0) {
      await this.stopVNCServices();
    }
  }

  /**
   * Get current VNC service status
   */
  getStatus(): VNCServiceStatus {
    return {
      isRunning: this.isRunning,
      xvfbPid: this.xvfbProcess?.pid,
      vncPid: this.vncProcess?.pid,
      noVncPid: this.noVncProcess?.pid,
      port: this.isRunning ? 5900 : undefined,
      webPort: this.isRunning ? 6080 : undefined
    };
  }

  /**
   * Check if VNC services are needed (any active non-headless executions)
   */
  hasActiveExecutions(): boolean {
    return this.activeExecutions.size > 0;
  }

  /**
   * Force stop all VNC services
   */
  async forceStop(): Promise<void> {
    console.log('🛑 Force stopping all VNC services');
    this.activeExecutions.clear();
    await this.stopVNCServices();
  }

  private async startVNCServices(): Promise<void> {
    if (this.isRunning) {
      console.log('VNC services already running');
      return;
    }

    try {
      console.log('🖥️ Starting Xvfb...');
      await this.startXvfb();

      console.log('📺 Starting VNC server...');
      await this.startVNCServer();

      console.log('🌐 Starting noVNC web interface...');
      await this.startNoVNC();

      this.isRunning = true;
      console.log('✅ VNC services started successfully');
    } catch (error) {
      console.error('❌ Failed to start VNC services:', error);
      await this.stopVNCServices();
      throw error;
    }
  }

  private async stopVNCServices(): Promise<void> {
    if (!this.isRunning) {
      console.log('VNC services already stopped');
      return;
    }

    console.log('🛑 Stopping VNC services...');

    // Stop noVNC
    if (this.noVncProcess) {
      this.noVncProcess.kill('SIGTERM');
      this.noVncProcess = null;
    }

    // Stop VNC server
    if (this.vncProcess) {
      this.vncProcess.kill('SIGTERM');
      this.vncProcess = null;
    }

    // Stop Xvfb
    if (this.xvfbProcess) {
      this.xvfbProcess.kill('SIGTERM');
      this.xvfbProcess = null;
    }

    // Kill any remaining processes
    try {
      await execAsync('pkill -f "websockify.*6080"').catch(() => {});
      await execAsync('pkill -f "x11vnc.*:99"').catch(() => {});
      await execAsync('pkill -f "Xvfb :99"').catch(() => {});
    } catch (error) {
      // Ignore errors when killing processes
    }

    this.isRunning = false;
    console.log('✅ VNC services stopped');
  }

  private async startXvfb(): Promise<void> {
    return new Promise((resolve, reject) => {
      // Check if running on Windows (development mode)
      if (process.platform === 'win32') {
        console.log('🖥️ Windows detected - using mock Xvfb for development');
        process.env.DISPLAY = ':99';
        resolve();
        return;
      }

      this.xvfbProcess = spawn('Xvfb', [':99', '-screen', '0', '1280x720x24'], {
        detached: false,
        stdio: 'pipe'
      });

      this.xvfbProcess.on('error', (error) => {
        console.error('Xvfb error:', error);
        reject(error);
      });

      this.xvfbProcess.on('exit', (code) => {
        if (code !== 0 && code !== null) {
          console.error(`Xvfb exited with code ${code}`);
        }
      });

      // Set DISPLAY environment variable
      process.env.DISPLAY = ':99';

      // Give Xvfb time to start
      setTimeout(() => {
        if (this.xvfbProcess && !this.xvfbProcess.killed) {
          resolve();
        } else {
          reject(new Error('Xvfb failed to start'));
        }
      }, 2000);
    });
  }

  private async startVNCServer(): Promise<void> {
    return new Promise((resolve, reject) => {
      // Check if running on Windows (development mode)
      if (process.platform === 'win32') {
        console.log('📺 Windows detected - using mock VNC server for development');
        resolve();
        return;
      }

      this.vncProcess = spawn('x11vnc', [
        '-display', ':99',
        '-nopw',
        '-listen', 'localhost',
        '-xkb',
        '-ncache', '10',
        '-ncache_cr',
        '-forever'
      ], {
        detached: false,
        stdio: 'pipe'
      });

      this.vncProcess.on('error', (error) => {
        console.error('VNC server error:', error);
        reject(error);
      });

      this.vncProcess.on('exit', (code) => {
        if (code !== 0 && code !== null) {
          console.error(`VNC server exited with code ${code}`);
        }
      });

      // Give VNC server time to start
      setTimeout(() => {
        if (this.vncProcess && !this.vncProcess.killed) {
          resolve();
        } else {
          reject(new Error('VNC server failed to start'));
        }
      }, 2000);
    });
  }

  private async startNoVNC(): Promise<void> {
    return new Promise((resolve, reject) => {
      // Check if running on Windows (development mode)
      if (process.platform === 'win32') {
        console.log('🌐 Windows detected - using mock noVNC for development');
        console.log('🔗 Mock VNC URL would be: http://localhost:6080/vnc.html?host=localhost&port=6080&autoconnect=true&resize=off');
        resolve();
        return;
      }

      this.noVncProcess = spawn('websockify', [
        '--web=/opt/noVNC',
        '6080',
        'localhost:5900'
      ], {
        detached: false,
        stdio: 'pipe',
        cwd: '/opt/noVNC'
      });

      this.noVncProcess.on('error', (error) => {
        console.error('noVNC error:', error);
        reject(error);
      });

      this.noVncProcess.on('exit', (code) => {
        if (code !== 0 && code !== null) {
          console.error(`noVNC exited with code ${code}`);
        }
      });

      // Give noVNC time to start
      setTimeout(() => {
        if (this.noVncProcess && !this.noVncProcess.killed) {
          resolve();
        } else {
          reject(new Error('noVNC failed to start'));
        }
      }, 2000);
    });
  }
}

export const vncService = VNCService.getInstance();
