import { StepDefinition, StepCategory, STEP_CATEGORIES } from './categories'

export const navigationSteps: StepDefinition[] = [
  {
    type: 'navigate',
    name: 'Navigate',
    icon: '🌐',
    description: 'Navigate to a URL'
  },
  {
    type: 'goBack',
    name: 'Go Back',
    icon: '⬅️',
    description: 'Go back in browser history'
  },
  {
    type: 'goForward',
    name: 'Go Forward',
    icon: '➡️',
    description: 'Go forward in browser history'
  },
  {
    type: 'reload',
    name: 'Reload',
    icon: '🔄',
    description: 'Reload the current page'
  }
]

export const navigationCategory: StepCategory = {
  name: STEP_CATEGORIES.NAVIGATION,
  icon: '🧭',
  steps: navigationSteps
}
