// Test script to manually execute a flow with VNC
// Using built-in fetch (Node.js 18+)

async function testManualExecution() {
  try {
    const response = await fetch('http://localhost:3002/api/executions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        flowId: 'xv9t9qfrkmdfq8fmn',
        isManualExecution: true,
        openVNCViewer: true
      })
    });

    const result = await response.json();
    console.log('Response:', JSON.stringify(result, null, 2));
    
    if (result.success && result.data && result.data.vncInfo) {
      console.log('VNC Info found:', result.data.vncInfo);
    } else {
      console.log('No VNC info in response');
    }
  } catch (error) {
    console.error('Error:', error);
  }
}

testManualExecution();
