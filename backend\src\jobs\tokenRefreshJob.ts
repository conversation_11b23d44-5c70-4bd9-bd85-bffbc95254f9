import { oauth2Service } from '../services/oauth2Service';

/**
 * Job for automatically refreshing OAuth2 tokens that are about to expire
 */
export class TokenRefreshJob {
  private intervalId: NodeJS.Timeout | null = null;
  private readonly intervalMs: number;

  constructor(intervalMinutes: number = 30) {
    this.intervalMs = intervalMinutes * 60 * 1000; // Convert to milliseconds
  }

  /**
   * Start the token refresh job
   */
  start(): void {
    if (this.intervalId) {
      console.log('Token refresh job is already running');
      return;
    }

    console.log(`Starting token refresh job (interval: ${this.intervalMs / 60000} minutes)`);
    
    // Run immediately on start
    this.runRefreshCheck();

    // Schedule periodic runs
    this.intervalId = setInterval(() => {
      this.runRefreshCheck();
    }, this.intervalMs);
  }

  /**
   * Stop the token refresh job
   */
  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
      console.log('Token refresh job stopped');
    }
  }

  /**
   * Run a single token refresh check
   */
  private async runRefreshCheck(): Promise<void> {
    try {
      console.log('Running token refresh check...');
      await oauth2Service.checkAndRefreshExpiredTokens();
    } catch (error) {
      console.error('Error in token refresh job:', error);
    }
  }

  /**
   * Check if the job is running
   */
  isRunning(): boolean {
    return this.intervalId !== null;
  }
}

// Export singleton instance
export const tokenRefreshJob = new TokenRefreshJob();
